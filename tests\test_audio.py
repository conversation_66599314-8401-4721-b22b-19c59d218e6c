#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import logging
import time
import numpy as np
import os
import sys
import threading

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Check for audio libraries
try:
    import pyaudio
    PYAUDIO_AVAILABLE = True
    logger.info("PyAudio is available")
except ImportError:
    PYAUDIO_AVAILABLE = False
    logger.warning("PyAudio is not available")

try:
    import sounddevice as sd
    SOUNDDEVICE_AVAILABLE = True
    logger.info("sounddevice is available")
except ImportError:
    SOUNDDEVICE_AVAILABLE = False
    logger.warning("sounddevice is not available")

try:
    import wave
    WAVE_AVAILABLE = True
    logger.info("wave module is available")
except ImportError:
    WAVE_AVAILABLE = False
    logger.warning("wave module is not available")

def test_pyaudio():
    """Test PyAudio functionality"""
    if not PYAUDIO_AVAILABLE:
        logger.error("Cannot test PyAudio: Not available")
        return False
    
    try:
        # Initialize PyAudio
        p = pyaudio.PyAudio()
        
        # Log device information
        device_count = p.get_device_count()
        logger.info(f"Found {device_count} audio devices")
        
        # Get default input device
        try:
            default_input = p.get_default_input_device_info()
            logger.info(f"Default input device: {default_input['name']} (index: {default_input['index']})")
        except Exception as e:
            logger.error(f"Error getting default input device: {str(e)}")
        
        # List all devices
        for i in range(device_count):
            try:
                device_info = p.get_device_info_by_index(i)
                logger.info(f"Device {i}: {device_info['name']}")
                logger.info(f"  Input channels: {device_info['maxInputChannels']}")
                logger.info(f"  Output channels: {device_info['maxOutputChannels']}")
                logger.info(f"  Default sample rate: {device_info['defaultSampleRate']}")
            except Exception as e:
                logger.error(f"Error getting info for device {i}: {str(e)}")
        
        # Try to record from default input device
        try:
            if p.get_default_input_device_info()['maxInputChannels'] > 0:
                logger.info("Testing recording from default input device...")
                
                # Open stream
                stream = p.open(
                    format=pyaudio.paInt16,
                    channels=1,
                    rate=16000,
                    input=True,
                    frames_per_buffer=1024
                )
                
                logger.info("Recording 3 seconds of audio...")
                frames = []
                for i in range(0, int(16000 / 1024 * 3)):
                    data = stream.read(1024, exception_on_overflow=False)
                    frames.append(data)
                    # Print audio levels to see if we're capturing anything
                    audio_array = np.frombuffer(data, dtype=np.int16)
                    level = np.abs(audio_array).mean()
                    logger.info(f"Audio level: {level:.2f}")
                
                logger.info("Finished recording")
                
                # Stop and close the stream
                stream.stop_stream()
                stream.close()
                
                # Save to file if wave is available
                if WAVE_AVAILABLE:
                    logger.info("Saving test recording to test_pyaudio.wav")
                    wf = wave.open("test_pyaudio.wav", 'wb')
                    wf.setnchannels(1)
                    wf.setsampwidth(p.get_sample_size(pyaudio.paInt16))
                    wf.setframerate(16000)
                    wf.writeframes(b''.join(frames))
                    wf.close()
                    logger.info(f"Saved test recording to {os.path.abspath('test_pyaudio.wav')}")
            else:
                logger.warning("Default input device has no input channels")
        except Exception as e:
            logger.error(f"Error testing recording: {str(e)}")
        
        # Clean up
        p.terminate()
        return True
    except Exception as e:
        logger.error(f"Error testing PyAudio: {str(e)}")
        return False

def test_sounddevice():
    """Test sounddevice functionality"""
    if not SOUNDDEVICE_AVAILABLE:
        logger.error("Cannot test sounddevice: Not available")
        return False
    
    try:
        # Log device information
        devices = sd.query_devices()
        logger.info(f"Found {len(devices)} audio devices via sounddevice")
        
        # Get default devices
        try:
            default_input = sd.default.device[0]
            default_output = sd.default.device[1]
            logger.info(f"Default input device: {devices[default_input]['name']} (index: {default_input})")
            logger.info(f"Default output device: {devices[default_output]['name']} (index: {default_output})")
        except Exception as e:
            logger.error(f"Error getting default devices: {str(e)}")
        
        # List all devices
        for i, device in enumerate(devices):
            logger.info(f"Device {i}: {device['name']}")
            logger.info(f"  Input channels: {device['max_input_channels']}")
            logger.info(f"  Output channels: {device['max_output_channels']}")
            logger.info(f"  Default sample rate: {device['default_samplerate']}")
        
        # Try to record from default input device
        try:
            if devices[default_input]['max_input_channels'] > 0:
                logger.info("Testing recording from default input device...")
                
                # Record audio
                duration = 3  # seconds
                fs = 16000  # Sample rate
                channels = 1
                
                logger.info(f"Recording {duration} seconds of audio...")
                recording = sd.rec(int(duration * fs), samplerate=fs, channels=channels, dtype='int16')
                
                # Show progress
                for i in range(duration):
                    logger.info(f"Recording: {i+1}/{duration} seconds")
                    time.sleep(1)
                
                sd.wait()  # Wait until recording is finished
                logger.info("Finished recording")
                
                # Check audio levels
                level = np.abs(recording).mean()
                logger.info(f"Average audio level: {level:.2f}")
                
                # Save to file if wave is available
                if WAVE_AVAILABLE:
                    logger.info("Saving test recording to test_sounddevice.wav")
                    wf = wave.open("test_sounddevice.wav", 'wb')
                    wf.setnchannels(channels)
                    wf.setsampwidth(2)  # 16-bit audio = 2 bytes
                    wf.setframerate(fs)
                    wf.writeframes(recording.tobytes())
                    wf.close()
                    logger.info(f"Saved test recording to {os.path.abspath('test_sounddevice.wav')}")
            else:
                logger.warning("Default input device has no input channels")
        except Exception as e:
            logger.error(f"Error testing recording: {str(e)}")
        
        return True
    except Exception as e:
        logger.error(f"Error testing sounddevice: {str(e)}")
        return False

def test_loopback_devices():
    """Test for loopback devices for system audio capture"""
    if not SOUNDDEVICE_AVAILABLE:
        logger.error("Cannot test loopback devices: sounddevice not available")
        return False
    
    try:
        devices = sd.query_devices()
        logger.info("Searching for potential loopback devices...")
        
        loopback_candidates = []
        
        for i, device in enumerate(devices):
            device_name = device['name'].lower()
            if ('loopback' in device_name or 
                'output' in device_name or 
                'mix' in device_name or 
                'what u hear' in device_name or
                'stereo mix' in device_name):
                if device.get('max_input_channels', 0) > 0:
                    loopback_candidates.append((i, device))
                    logger.info(f"Found potential loopback device: {device['name']} (index: {i})")
        
        if not loopback_candidates:
            logger.warning("No loopback devices found for system audio capture")
        
        return True
    except Exception as e:
        logger.error(f"Error testing loopback devices: {str(e)}")
        return False

def test_audio_generation():
    """Test synthetic audio generation"""
    try:
        logger.info("Testing synthetic audio generation...")
        
        # Generate a simple sine wave
        sample_rate = 16000
        duration = 3  # seconds
        frequency = 440  # 440 Hz = A4 note
        
        t = np.linspace(0, duration, int(sample_rate * duration), False)
        audio_array = np.sin(2 * np.pi * frequency * t) * 32767 / 4  # Quarter amplitude
        audio_array = audio_array.astype(np.int16)
        
        # Check audio data
        logger.info(f"Generated {len(audio_array)} samples of synthetic audio")
        logger.info(f"Audio shape: {audio_array.shape}")
        logger.info(f"Audio min: {audio_array.min()}, max: {audio_array.max()}, mean: {audio_array.mean():.2f}")
        
        # Save to file if wave is available
        if WAVE_AVAILABLE:
            logger.info("Saving synthetic audio to test_synthetic.wav")
            wf = wave.open("test_synthetic.wav", 'wb')
            wf.setnchannels(1)
            wf.setsampwidth(2)  # 16-bit audio = 2 bytes
            wf.setframerate(sample_rate)
            wf.writeframes(audio_array.tobytes())
            wf.close()
            logger.info(f"Saved synthetic audio to {os.path.abspath('test_synthetic.wav')}")
        
        return True
    except Exception as e:
        logger.error(f"Error testing audio generation: {str(e)}")
        return False

def test_from_backend():
    """Test audio capture using the actual backend module"""
    try:
        # Import the AudioCapture class from our backend
        sys.path.append(os.path.abspath(os.path.dirname(__file__)))
        from backend.audio_capture import AudioCapture
        
        logger.info("Testing AudioCapture from backend module...")
        
        # Create a simple callback function
        audio_chunks = []
        def on_audio_chunk(audio_chunk):
            audio_chunks.append(audio_chunk)
            level = np.abs(audio_chunk).mean()
            logger.info(f"Received audio chunk: {len(audio_chunk)} samples, level: {level:.2f}")
        
        # Create the AudioCapture instance
        recorder = AudioCapture(on_audio_chunk=on_audio_chunk)
        
        # Get available devices
        devices = recorder.get_available_devices()
        logger.info("Available input devices:")
        for device in devices:
            logger.info(f"  {device['index']}: {device['name']} (Channels: {device['channels']})" + 
                  (" (Default)" if device['default'] else ""))
        
        # Start recording
        logger.info("Starting recording for 5 seconds...")
        recorder.start_recording()
        
        # Wait for 5 seconds
        for i in range(5):
            logger.info(f"Recording: {i+1}/5 seconds")
            time.sleep(1)
        
        # Stop recording
        recorder.stop_recording()
        
        # Check results
        logger.info(f"Captured {len(audio_chunks)} audio chunks")
        
        if audio_chunks:
            total_samples = sum(len(chunk) for chunk in audio_chunks)
            logger.info(f"Total samples captured: {total_samples}")
            
            # Calculate average level
            all_audio = np.concatenate(audio_chunks) if audio_chunks else np.array([])
            if len(all_audio) > 0:
                level = np.abs(all_audio).mean()
                logger.info(f"Average audio level: {level:.2f}")
            
            # Save to file
            recorder.save_to_file("test_backend.wav")
            logger.info(f"Saved recording to {os.path.abspath('test_backend.wav')}")
        else:
            logger.warning("No audio chunks were captured")
        
        return True
    except Exception as e:
        logger.error(f"Error testing backend AudioCapture: {str(e)}")
        return False

def main():
    """Run all audio tests"""
    logger.info("===== AUDIO CAPTURE TEST UTILITY =====")
    logger.info(f"Running on: {sys.platform}")
    logger.info(f"Current directory: {os.path.abspath(os.curdir)}")
    logger.info("====================================")
    
    # Test PyAudio
    logger.info("\n===== TESTING PYAUDIO =====")
    test_pyaudio()
    
    # Test sounddevice
    logger.info("\n===== TESTING SOUNDDEVICE =====")
    test_sounddevice()
    
    # Test loopback devices
    logger.info("\n===== TESTING LOOPBACK DEVICES =====")
    test_loopback_devices()
    
    # Test audio generation
    logger.info("\n===== TESTING AUDIO GENERATION =====")
    test_audio_generation()
    
    # Test from backend
    logger.info("\n===== TESTING BACKEND AUDIO CAPTURE =====")
    test_from_backend()
    
    logger.info("\n===== ALL TESTS COMPLETED =====")
    logger.info("Check the log output above for results")
    logger.info("If test_*.wav files were created, you can play them to verify audio capture")

if __name__ == "__main__":
    main()
    # Keep console open
    input("Press Enter to exit...")
