#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import threading
import time
import os
import json
import requests
import logging
import sys
from datetime import datetime

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("simplified_app.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class Config:
    """Configuration manager"""
    
    def __init__(self, config_file="config.json"):
        self.config_file = config_file
        self.config = {
            # Ollama settings
            "ollama_host": "http://CarlsMacStudio.got.volvo.net:11434",
            "transcription_model": "dimavz/whisper-tiny",
            "summarization_model": "qwen3",
            
            # Application settings
            "temp_dir": os.path.join(os.path.expanduser("~"), "SumOnTheFly", "temp")
        }
        self.load()
        
        # Ensure temp directory exists
        os.makedirs(self.config["temp_dir"], exist_ok=True)
    
    def load(self):
        """Load configuration from file"""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r') as f:
                    loaded_config = json.load(f)
                    self.config.update(loaded_config)
                logger.info(f"Loaded configuration from {self.config_file}")
        except Exception as e:
            logger.error(f"Error loading configuration: {str(e)}")
    
    def save(self):
        """Save configuration to file"""
        try:
            with open(self.config_file, 'w') as f:
                json.dump(self.config, f, indent=4)
            logger.info(f"Saved configuration to {self.config_file}")
            return True
        except Exception as e:
            logger.error(f"Error saving configuration: {str(e)}")
            return False
    
    def get(self, key, default=None):
        """Get a configuration value"""
        return self.config.get(key, default)
    
    def set(self, key, value):
        """Set a configuration value"""
        self.config[key] = value

class TranscriptionService:
    """Service for transcribing audio using Ollama"""
    
    def __init__(self, ollama_host, model="whisper"):
        self.ollama_host = ollama_host
        self.model = model
        self.test_connection()
    
    def test_connection(self):
        """Test connection to Ollama server"""
        try:
            response = requests.get(f"{self.ollama_host}/api/version")
            if response.status_code == 200:
                version = response.json().get("version", "unknown")
                logger.info(f"Connected to Ollama server (version {version})")
                return True
            else:
                logger.warning(f"Failed to connect to Ollama server: {response.status_code}")
                return False
        except Exception as e:
            logger.warning(f"Error connecting to Ollama server: {str(e)}")
            return False
    
    def transcribe_audio_file(self, audio_file, callback=None):
        """Transcribe audio file using Ollama"""
        thread = threading.Thread(
            target=self._transcribe_file,
            args=(audio_file, callback),
            daemon=True
        )
        thread.start()
    
    def _transcribe_file(self, audio_file, callback):
        """Transcribe audio file in a separate thread"""
        try:
            # Send to Ollama for transcription
            url = f"{self.ollama_host}/api/audio"
            
            with open(audio_file, 'rb') as f:
                files = {
                    'file': (os.path.basename(audio_file), f, 'audio/wav')
                }
                data = {
                    'model': self.model,
                    'format': 'json',
                    'language': 'en'
                }
                
                response = requests.post(url, files=files, data=data)
            
            if response.status_code == 200:
                result = response.json()
                text = result.get('text', '')
                if text and callback:
                    callback(text)
            else:
                logger.warning(f"Transcription failed: {response.status_code} - {response.text}")
                
        except Exception as e:
            logger.error(f"Error in transcription: {str(e)}")

class SummarizationService:
    """Service for summarizing text using Ollama"""
    
    def __init__(self, ollama_host, model="llama2"):
        self.ollama_host = ollama_host
        self.model = model
        self.test_connection()
    
    def test_connection(self):
        """Test connection to Ollama server"""
        try:
            response = requests.get(f"{self.ollama_host}/api/version")
            if response.status_code == 200:
                version = response.json().get("version", "unknown")
                logger.info(f"Connected to Ollama server (version {version})")
                return True
            else:
                logger.warning(f"Failed to connect to Ollama server: {response.status_code}")
                return False
        except Exception as e:
            logger.warning(f"Error connecting to Ollama server: {str(e)}")
            return False
    
    def summarize_text(self, text, prompt_template, callback=None):
        """Summarize text using Ollama"""
        thread = threading.Thread(
            target=self._generate_summary,
            args=(text, prompt_template, callback),
            daemon=True
        )
        thread.start()
    
    def _generate_summary(self, text, prompt_template, callback):
        """Generate summary using Ollama"""
        try:
            # Prepare prompt
            prompt = f"{prompt_template}\n\nText to summarize:\n{text}"
            
            # Send to Ollama
            url = f"{self.ollama_host}/api/generate"
            data = {
                "model": self.model,
                "prompt": prompt,
                "stream": False
            }
            
            response = requests.post(url, json=data)
            
            if response.status_code == 200:
                result = response.json()
                summary = result.get('response', '')
                if summary and callback:
                    callback(summary)
            else:
                logger.warning(f"Summarization failed: {response.status_code} - {response.text}")
                
        except Exception as e:
            logger.error(f"Error in summarization: {str(e)}")

class SimplifiedApp:
    """Simplified application that focuses on transcription and summarization"""
    
    def __init__(self, root):
        self.root = root
        self.root.title("SumOnTheFly - Simplified")
        self.root.geometry("800x600")
        self.root.minsize(600, 400)
        
        # Initialize components
        self.config = Config()
        
        # Services
        self.transcription_service = TranscriptionService(
            ollama_host=self.config.get("ollama_host"),
            model=self.config.get("transcription_model")
        )
        
        self.summarization_service = SummarizationService(
            ollama_host=self.config.get("ollama_host"),
            model=self.config.get("summarization_model")
        )
        
        # Set up UI
        self.setup_ui()
    
    def setup_ui(self):
        """Set up the user interface"""
        # Main frame
        main_frame = ttk.Frame(self.root, padding=10)
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # Create menu
        self.create_menu()
        
        # Input panel
        input_frame = ttk.LabelFrame(main_frame, text="Input")
        input_frame.pack(fill=tk.X, pady=5)
        
        # File input
        file_frame = ttk.Frame(input_frame)
        file_frame.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Label(file_frame, text="Audio File:").pack(side=tk.LEFT, padx=5)
        
        self.file_var = tk.StringVar()
        ttk.Entry(file_frame, textvariable=self.file_var, width=50).pack(side=tk.LEFT, padx=5, fill=tk.X, expand=True)
        
        ttk.Button(file_frame, text="Browse", command=self.browse_file).pack(side=tk.LEFT, padx=5)
        ttk.Button(file_frame, text="Transcribe", command=self.transcribe_file).pack(side=tk.LEFT, padx=5)
        
        # Text input
        text_frame = ttk.Frame(input_frame)
        text_frame.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Label(text_frame, text="Or enter text directly:").pack(anchor=tk.W, padx=5, pady=2)
        
        self.input_text = tk.Text(text_frame, height=5, wrap=tk.WORD)
        self.input_text.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Button(text_frame, text="Summarize Text", command=self.summarize_input_text).pack(anchor=tk.E, padx=5, pady=5)
        
        # Transcription panel
        transcription_frame = ttk.LabelFrame(main_frame, text="Transcription")
        transcription_frame.pack(fill=tk.BOTH, expand=True, pady=5)
        
        self.transcription_text = tk.Text(
            transcription_frame,
            wrap=tk.WORD,
            state=tk.NORMAL
        )
        transcription_scroll = ttk.Scrollbar(
            transcription_frame, 
            command=self.transcription_text.yview
        )
        self.transcription_text.configure(yscrollcommand=transcription_scroll.set)
        
        self.transcription_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        transcription_scroll.pack(side=tk.RIGHT, fill=tk.Y)
        
        # Summary panel
        summary_frame = ttk.LabelFrame(main_frame, text="Summary")
        summary_frame.pack(fill=tk.BOTH, expand=True, pady=5)
        
        self.summary_text = tk.Text(
            summary_frame,
            wrap=tk.WORD,
            state=tk.NORMAL,
            height=10
        )
        summary_scroll = ttk.Scrollbar(
            summary_frame, 
            command=self.summary_text.yview
        )
        self.summary_text.configure(yscrollcommand=summary_scroll.set)
        
        self.summary_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        summary_scroll.pack(side=tk.RIGHT, fill=tk.Y)
        
        # Status bar
        self.status_bar = ttk.Label(
            self.root, 
            text="Ready", 
            relief=tk.SUNKEN, 
            anchor=tk.W
        )
        self.status_bar.pack(side=tk.BOTTOM, fill=tk.X)
    
    def create_menu(self):
        """Create the application menu"""
        menubar = tk.Menu(self.root)
        
        # File menu
        file_menu = tk.Menu(menubar, tearoff=0)
        file_menu.add_command(label="Open Audio File", command=self.browse_file)
        file_menu.add_command(label="Clear All", command=self.clear_all)
        file_menu.add_separator()
        file_menu.add_command(label="Exit", command=self.root.quit)
        menubar.add_cascade(label="File", menu=file_menu)
        
        # Settings menu
        settings_menu = tk.Menu(menubar, tearoff=0)
        settings_menu.add_command(label="Configure", command=self.show_settings)
        menubar.add_cascade(label="Settings", menu=settings_menu)
        
        # Help menu
        help_menu = tk.Menu(menubar, tearoff=0)
        help_menu.add_command(label="About", command=self.show_about)
        menubar.add_cascade(label="Help", menu=help_menu)
        
        self.root.config(menu=menubar)
    
    def browse_file(self):
        """Browse for an audio file"""
        file_path = filedialog.askopenfilename(
            title="Select Audio File",
            filetypes=[("WAV files", "*.wav"), ("All files", "*.*")]
        )
        
        if file_path:
            self.file_var.set(file_path)
    
    def transcribe_file(self):
        """Transcribe the selected audio file"""
        file_path = self.file_var.get()
        
        if not file_path or not os.path.exists(file_path):
            messagebox.showerror("Error", "Please select a valid audio file.")
            return
        
        # Clear previous data
        self.transcription_text.delete(1.0, tk.END)
        self.summary_text.delete(1.0, tk.END)
        
        # Update status
        self.status_bar.config(text=f"Transcribing {os.path.basename(file_path)}...")
        
        # Transcribe the file
        self.transcription_service.transcribe_audio_file(
            file_path,
            self.on_transcription
        )
    
    def summarize_input_text(self):
        """Summarize the text entered directly"""
        text = self.input_text.get(1.0, tk.END).strip()
        
        if not text:
            messagebox.showerror("Error", "Please enter some text to summarize.")
            return
        
        # Update transcription area
        self.transcription_text.delete(1.0, tk.END)
        self.transcription_text.insert(tk.END, text)
        
        # Clear summary
        self.summary_text.delete(1.0, tk.END)
        
        # Update status
        self.status_bar.config(text="Generating summary...")
        
        # Generate summary
        self.summarization_service.summarize_text(
            text,
            "Summarize the key points in 3-5 bullets.",
            self.on_summary
        )
    
    def on_transcription(self, text):
        """Handle transcription result"""
        if not text:
            self.status_bar.config(text="Transcription failed or returned empty result.")
            return
        
        # Update the UI
        self.transcription_text.delete(1.0, tk.END)
        self.transcription_text.insert(tk.END, text)
        
        # Update status
        self.status_bar.config(text="Transcription complete. Generating summary...")
        
        # Generate summary
        self.summarization_service.summarize_text(
            text,
            "Summarize the key points in 3-5 bullets.",
            self.on_summary
        )
    
    def on_summary(self, summary):
        """Handle summary result"""
        if not summary:
            self.status_bar.config(text="Summary generation failed or returned empty result.")
            return
        
        # Update the UI
        self.summary_text.delete(1.0, tk.END)
        self.summary_text.insert(tk.END, summary)
        
        # Update status
        self.status_bar.config(text="Summary generated.")
    
    def clear_all(self):
        """Clear all input and output fields"""
        self.file_var.set("")
        self.input_text.delete(1.0, tk.END)
        self.transcription_text.delete(1.0, tk.END)
        self.summary_text.delete(1.0, tk.END)
        self.status_bar.config(text="Ready")
    
    def show_settings(self):
        """Show the settings dialog"""
        # Create a new top-level window
        settings_window = tk.Toplevel(self.root)
        settings_window.title("Settings")
        settings_window.geometry("500x300")
        settings_window.transient(self.root)  # Make it modal
        settings_window.grab_set()
        
        # Create a notebook for tabbed settings
        notebook = ttk.Notebook(settings_window)
        notebook.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # Ollama settings tab
        ollama_frame = ttk.Frame(notebook, padding=10)
        notebook.add(ollama_frame, text="Ollama")
        
        ttk.Label(ollama_frame, text="Ollama Host:").grid(row=0, column=0, sticky=tk.W, pady=5)
        ollama_host = tk.StringVar(value=self.config.get("ollama_host", "http://localhost:11434"))
        ttk.Entry(ollama_frame, textvariable=ollama_host, width=40).grid(row=0, column=1, sticky=tk.W, pady=5)
        
        ttk.Label(ollama_frame, text="Transcription Model:").grid(row=1, column=0, sticky=tk.W, pady=5)
        transcription_model = tk.StringVar(value=self.config.get("transcription_model", "whisper"))
        ttk.Entry(ollama_frame, textvariable=transcription_model).grid(row=1, column=1, sticky=tk.W, pady=5)
        
        ttk.Label(ollama_frame, text="Summarization Model:").grid(row=2, column=0, sticky=tk.W, pady=5)
        summarization_model = tk.StringVar(value=self.config.get("summarization_model", "llama2"))
        ttk.Entry(ollama_frame, textvariable=summarization_model).grid(row=2, column=1, sticky=tk.W, pady=5)
        
        # Buttons
        def save_settings():
            try:
                # Update config
                self.config.set("ollama_host", ollama_host.get())
                self.config.set("transcription_model", transcription_model.get())
                self.config.set("summarization_model", summarization_model.get())
                
                # Save config
                self.config.save()
                
                # Reinitialize services
                self.transcription_service = TranscriptionService(
                    ollama_host=self.config.get("ollama_host"),
                    model=self.config.get("transcription_model")
                )
                
                self.summarization_service = SummarizationService(
                    ollama_host=self.config.get("ollama_host"),
                    model=self.config.get("summarization_model")
                )
                
                messagebox.showinfo("Success", "Settings saved successfully")
                settings_window.destroy()
            except Exception as e:
                messagebox.showerror("Error", f"Failed to save settings: {str(e)}")
        
        button_frame = ttk.Frame(settings_window)
        button_frame.pack(fill=tk.X, padx=10, pady=10)
        
        ttk.Button(button_frame, text="Save", command=save_settings).pack(side=tk.RIGHT, padx=5)
        ttk.Button(button_frame, text="Cancel", command=settings_window.destroy).pack(side=tk.RIGHT, padx=5)
    
    def show_about(self):
        """Show the about dialog"""
        about_text = """SumOnTheFly - Simplified Edition v1.0
        
A desktop application that transcribes audio files and generates bullet-point summaries.

Created with Python and Tkinter.
Uses Ollama for local AI processing.

Ollama Server: http://CarlsMacStudio.got.volvo.net:11434
Transcription Model: dimavz/whisper-tiny
Summarization Model: qwen3
        """
        messagebox.showinfo("About SumOnTheFly", about_text)

def main():
    """Main entry point for the application"""
    try:
        # Create the root window
        root = tk.Tk()
        
        # Create and run the application
        app = SimplifiedApp(root)
        
        root.mainloop()
        
    except Exception as e:
        logger.critical(f"Unhandled exception: {str(e)}")
        messagebox.showerror("Critical Error", f"An unhandled error occurred: {str(e)}")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
