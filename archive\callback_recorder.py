#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import pyaudio
import wave
import numpy as np
import time
import os
from datetime import datetime
import logging

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("callback_recorder.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class CallbackRecorder:
    def __init__(self, root):
        self.root = root
        self.root.title("Callback Audio Recorder")
        self.root.geometry("500x300")
        
        # Audio parameters
        self.chunk = 1024
        self.format = pyaudio.paInt16
        self.channels = 1
        self.rate = 16000
        
        # State variables
        self.frames = []
        self.is_recording = False
        self.p = None
        self.stream = None
        self.audio_level = 0
        
        # Create UI
        self.create_widgets()
        
        # Start UI update timer
        self.update_ui()
    
    def create_widgets(self):
        # Main frame
        frame = ttk.Frame(self.root, padding=10)
        frame.pack(fill=tk.BOTH, expand=True)
        
        # Buttons frame
        button_frame = ttk.Frame(frame)
        button_frame.pack(fill=tk.X, pady=10)
        
        self.record_button = ttk.Button(
            button_frame, 
            text="Start Recording", 
            command=self.toggle_recording
        )
        self.record_button.pack(side=tk.LEFT, padx=5)
        
        self.save_button = ttk.Button(
            button_frame, 
            text="Save Recording", 
            command=self.save_recording, 
            state=tk.DISABLED
        )
        self.save_button.pack(side=tk.LEFT, padx=5)
        
        # Status and time frame
        info_frame = ttk.Frame(frame)
        info_frame.pack(fill=tk.X, pady=10)
        
        self.status_label = ttk.Label(info_frame, text="Ready")
        self.status_label.pack(side=tk.LEFT, padx=5)
        
        self.time_label = ttk.Label(info_frame, text="00:00")
        self.time_label.pack(side=tk.RIGHT, padx=5)
        
        # Audio level visualization
        level_frame = ttk.LabelFrame(frame, text="Audio Level")
        level_frame.pack(fill=tk.X, pady=10)
        
        self.level_canvas = tk.Canvas(level_frame, height=30, bg="white")
        self.level_canvas.pack(fill=tk.X, padx=5, pady=5)
    
    def update_ui(self):
        """Periodically update the UI"""
        if self.is_recording:
            # Update recording time
            elapsed = time.time() - self.start_time
            mins, secs = divmod(int(elapsed), 60)
            self.time_label.config(text=f"{mins:02d}:{secs:02d}")
            
            # Update audio level visualization
            self.update_level_visualization(self.audio_level)
        
        # Schedule next update
        self.root.after(100, self.update_ui)
    
    def update_level_visualization(self, level):
        """Update the audio level visualization"""
        self.level_canvas.delete("all")
        width = self.level_canvas.winfo_width()
        height = self.level_canvas.winfo_height()
        
        # Draw background
        self.level_canvas.create_rectangle(0, 0, width, height, fill="white")
        
        # Draw level bar
        bar_width = int(width * (level / 100))
        
        # Color based on level
        if level < 30:
            color = "green"
        elif level < 70:
            color = "yellow"
        else:
            color = "red"
        
        self.level_canvas.create_rectangle(0, 0, bar_width, height, fill=color)
    
    def audio_callback(self, in_data, frame_count, time_info, status):
        """Callback function for audio stream"""
        if status:
            logger.warning(f"Audio stream status: {status}")
        
        # Store the audio data
        self.frames.append(in_data)
        
        # Calculate audio level for visualization
        try:
            audio_array = np.frombuffer(in_data, dtype=np.int16)
            level = np.abs(audio_array).mean()
            self.audio_level = min(100, level / 50)  # Normalize to 0-100
        except Exception as e:
            logger.error(f"Error calculating audio level: {e}")
            self.audio_level = 0
        
        return (in_data, pyaudio.paContinue)
    
    def toggle_recording(self):
        """Start or stop recording"""
        if not self.is_recording:
            self.start_recording()
        else:
            self.stop_recording()
    
    def start_recording(self):
        """Start recording audio using callback"""
        try:
            # Initialize PyAudio
            self.p = pyaudio.PyAudio()
            
            # Clear previous recording
            self.frames = []
            
            # Open stream with callback
            self.stream = self.p.open(
                format=self.format,
                channels=self.channels,
                rate=self.rate,
                input=True,
                frames_per_buffer=self.chunk,
                stream_callback=self.audio_callback
            )
            
            # Start the stream
            self.stream.start_stream()
            
            # Update state and UI
            self.is_recording = True
            self.start_time = time.time()
            self.record_button.config(text="Stop Recording")
            self.save_button.config(state=tk.DISABLED)
            self.status_label.config(text="Recording...")
            
            logger.info("Started recording with callback")
            
        except Exception as e:
            logger.error(f"Error starting recording: {e}")
            messagebox.showerror("Error", f"Could not start recording: {str(e)}")
            self.cleanup()
    
    def stop_recording(self):
        """Stop recording audio"""
        if not self.is_recording:
            return
        
        try:
            # Stop recording
            self.is_recording = False
            
            # Stop and close the stream
            if self.stream:
                self.stream.stop_stream()
                self.stream.close()
                self.stream = None
            
            # Terminate PyAudio
            if self.p:
                self.p.terminate()
                self.p = None
            
            # Update UI
            self.record_button.config(text="Start Recording")
            self.save_button.config(state=tk.NORMAL if self.frames else tk.DISABLED)
            self.status_label.config(text="Recording stopped")
            self.audio_level = 0
            
            logger.info("Stopped recording")
            
        except Exception as e:
            logger.error(f"Error stopping recording: {e}")
            messagebox.showerror("Error", f"Error stopping recording: {str(e)}")
        finally:
            self.cleanup()
    
    def save_recording(self):
        """Save the recorded audio to a WAV file"""
        if not self.frames:
            messagebox.showinfo("Info", "No audio recorded")
            return
        
        try:
            # Ask for save location
            filename = filedialog.asksaveasfilename(
                defaultextension=".wav",
                filetypes=[("WAV files", "*.wav")],
                initialfile=f"recording_{datetime.now().strftime('%Y%m%d_%H%M%S')}.wav"
            )
            
            if not filename:
                return
            
            # Save as WAV file
            with wave.open(filename, 'wb') as wf:
                wf.setnchannels(self.channels)
                wf.setsampwidth(pyaudio.PyAudio().get_sample_size(self.format))
                wf.setframerate(self.rate)
                wf.writeframes(b''.join(self.frames))
            
            messagebox.showinfo("Success", f"Recording saved to {filename}")
            self.status_label.config(text=f"Saved to {os.path.basename(filename)}")
            
            logger.info(f"Saved recording to {filename}")
            
        except Exception as e:
            logger.error(f"Error saving recording: {e}")
            messagebox.showerror("Error", f"Could not save recording: {str(e)}")
    
    def cleanup(self):
        """Clean up resources"""
        if hasattr(self, 'stream') and self.stream:
            try:
                self.stream.stop_stream()
                self.stream.close()
                self.stream = None
            except Exception as e:
                logger.error(f"Error closing stream: {e}")
        
        if hasattr(self, 'p') and self.p:
            try:
                self.p.terminate()
                self.p = None
            except Exception as e:
                logger.error(f"Error terminating PyAudio: {e}")

def main():
    root = tk.Tk()
    app = CallbackRecorder(root)
    
    # Handle window close
    def on_closing():
        if app.is_recording:
            if messagebox.askyesno("Confirm", "Recording in progress. Stop and exit?"):
                app.stop_recording()
                root.destroy()
        else:
            root.destroy()
    
    root.protocol("WM_DELETE_WINDOW", on_closing)
    root.mainloop()

if __name__ == "__main__":
    main()
