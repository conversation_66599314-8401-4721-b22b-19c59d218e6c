# Audio processing
# Note: PyAudio can be difficult to install on Windows. Try pip install pipwin && pipwin install pyaudio if this fails
sounddevice>=0.4.6
numpy>=1.24.0
pydub>=0.25.1

# AI processing with Ollama
requests>=2.31.0
openai>=1.6.0

# Database (sqlite3 is included in Python standard library)
# sqlite3-api==1.0.2  # Removed - not needed, sqlite3 is built-in

# Export functionality
python-docx>=0.8.11
reportlab>=4.0.4

# Testing
pytest>=7.4.0

# UI (Tkinter is included in standard Python library)
pillow>=10.0.0  # For image handling in Tkinter

# Packaging
pyinstaller>=6.0.0

# Utilities
python-dotenv>=1.0.0
