#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import threading
import time
import numpy as np
import logging
import sys
import os
from datetime import datetime
from pathlib import Path
import tempfile

# OpenAI and dotenv imports
from dotenv import load_dotenv
from openai import OpenAI

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("openai_transcriber.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# Check for PyAudio
try:
    import pyaudio
    import wave
    AUDIO_AVAILABLE = True
    logger.info("PyAudio and wave modules are available")
except ImportError as e:
    AUDIO_AVAILABLE = False
    logger.error(f"Audio modules not available: {str(e)}")

# Load environment variables
load_dotenv()

# Initialize OpenAI client with Ollama backend
client = OpenAI(
    base_url=os.getenv("OLLAMA_URL", "http://CarlsMacStudio.got.volvo.net:11434"),
    api_key="ollama",
)

# Define transcription function
def transcribe_audio_file(file_path):
    """Transcribe an audio file using the OpenAI client with Ollama backend."""
    try:
        with open(file_path, "rb") as audio_file:
            transcription = client.audio.transcriptions.create(
                file=audio_file,
                model="dimavz/whisper-tiny:latest",
                response_format="text"
            )
        return transcription
    except Exception as e:
        logger.error(f"Transcription error: {str(e)}")
        return f"Error: {str(e)}"

class OpenAITranscriber:
    """Audio recorder with OpenAI transcription"""
    
    def __init__(self, root):
        self.root = root
        self.root.title("SumOnTheFly - OpenAI Transcriber")
        self.root.geometry("800x600")
        
        # Audio parameters
        self.sample_rate = 16000
        self.channels = 1
        self.chunk_size = 1024
        self.format = pyaudio.paInt16 if AUDIO_AVAILABLE else None
        
        # State variables
        self.is_recording = False
        self.audio_data = []
        self.selected_device_index = None
        self.p = None
        self.stream = None
        self.recording_thread = None
        self.recording_start_time = 0
        self.audio_level = 0
        self.temp_file = None
        
        # Set up UI
        self.setup_ui()
        
        # Detect audio devices
        self.detect_audio_devices()
        
        # Start UI update timer
        self.update_ui()
    
    def setup_ui(self):
        """Set up the user interface"""
        # Main frame
        main_frame = ttk.Frame(self.root, padding=10)
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # Connection info
        connection_frame = ttk.Frame(main_frame)
        connection_frame.pack(fill=tk.X, pady=5)
        
        ollama_url = os.getenv("OLLAMA_URL", "http://CarlsMacStudio.got.volvo.net:11434")
        connection_label = ttk.Label(
            connection_frame,
            text=f"Connected to: {ollama_url}",
            font=("Arial", 9)
        )
        connection_label.pack(pady=2)
        
        # Device selection
        device_frame = ttk.LabelFrame(main_frame, text="Audio Device")
        device_frame.pack(fill=tk.X, pady=5)
        
        self.device_var = tk.StringVar()
        self.device_dropdown = ttk.Combobox(device_frame, textvariable=self.device_var, state="readonly", width=40)
        self.device_dropdown.pack(side=tk.LEFT, padx=5, pady=5, fill=tk.X, expand=True)
        
        refresh_button = ttk.Button(device_frame, text="Refresh", command=self.detect_audio_devices)
        refresh_button.pack(side=tk.RIGHT, padx=5, pady=5)
        
        # Control panel
        control_frame = ttk.LabelFrame(main_frame, text="Controls")
        control_frame.pack(fill=tk.X, pady=5)
        
        self.record_button = ttk.Button(
            control_frame, 
            text="Start Recording", 
            command=self.toggle_recording
        )
        self.record_button.pack(side=tk.LEFT, padx=5, pady=5)
        
        self.transcribe_button = ttk.Button(
            control_frame, 
            text="Transcribe", 
            command=self.transcribe_recording,
            state=tk.DISABLED
        )
        self.transcribe_button.pack(side=tk.LEFT, padx=5, pady=5)
        
        self.save_button = ttk.Button(
            control_frame, 
            text="Save Recording", 
            command=self.save_recording,
            state=tk.DISABLED
        )
        self.save_button.pack(side=tk.LEFT, padx=5, pady=5)
        
        self.save_transcript_button = ttk.Button(
            control_frame, 
            text="Save Transcript", 
            command=self.save_transcript,
            state=tk.DISABLED
        )
        self.save_transcript_button.pack(side=tk.LEFT, padx=5, pady=5)
        
        self.time_label = ttk.Label(control_frame, text="00:00:00")
        self.time_label.pack(side=tk.RIGHT, padx=5, pady=5)
        
        # Audio level visualization
        level_frame = ttk.LabelFrame(main_frame, text="Audio Level")
        level_frame.pack(fill=tk.X, pady=5)
        
        self.level_canvas = tk.Canvas(level_frame, height=30, bg="white")
        self.level_canvas.pack(fill=tk.X, padx=5, pady=5)
        
        self.level_label = ttk.Label(level_frame, text="Level: 0.0%")
        self.level_label.pack(pady=2)
        
        # Transcription panel
        transcription_frame = ttk.LabelFrame(main_frame, text="Transcription")
        transcription_frame.pack(fill=tk.BOTH, expand=True, pady=5)
        
        self.transcription_text = tk.Text(
            transcription_frame,
            wrap=tk.WORD,
            height=10
        )
        transcription_scroll = ttk.Scrollbar(
            transcription_frame, 
            command=self.transcription_text.yview
        )
        self.transcription_text.configure(yscrollcommand=transcription_scroll.set)
        
        self.transcription_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=5, pady=5)
        transcription_scroll.pack(side=tk.RIGHT, fill=tk.Y, pady=5)
        
        # Status bar
        self.status_bar = ttk.Label(
            self.root, 
            text="Ready", 
            relief=tk.SUNKEN, 
            anchor=tk.W
        )
        self.status_bar.pack(side=tk.BOTTOM, fill=tk.X)
    
    def detect_audio_devices(self):
        """Detect available audio devices"""
        if not AUDIO_AVAILABLE:
            messagebox.showerror("Error", "PyAudio is not available. Please install it to use audio recording.")
            return
        
        try:
            p = pyaudio.PyAudio()
            device_count = p.get_device_count()
            devices = []
            device_indices = []
            
            # Get all input devices
            for i in range(device_count):
                try:
                    device_info = p.get_device_info_by_index(i)
                    if device_info.get('maxInputChannels', 0) > 0:
                        devices.append(f"{device_info.get('name')}")
                        device_indices.append(i)
                        logger.info(f"Found input device: {device_info.get('name')} (index: {i})")
                except Exception as e:
                    logger.warning(f"Error getting info for device {i}: {str(e)}")
            
            # Update dropdown
            self.device_dropdown['values'] = devices
            if devices:
                self.device_dropdown.current(0)
                self.selected_device_index = device_indices[0]
                self.status_bar.config(text=f"Found {len(devices)} audio devices")
            else:
                self.status_bar.config(text="No audio input devices found")
            
            # Clean up
            p.terminate()
            
        except Exception as e:
            logger.error(f"Error detecting audio devices: {str(e)}")
            messagebox.showerror("Error", f"Error detecting audio devices: {str(e)}")
    
    def update_ui(self):
        """Periodically update the UI"""
        if self.is_recording:
            # Update recording time
            elapsed = time.time() - self.recording_start_time
            hours, remainder = divmod(int(elapsed), 3600)
            minutes, seconds = divmod(remainder, 60)
            self.time_label.config(text=f"{hours:02d}:{minutes:02d}:{seconds:02d}")
            
            # Update audio level visualization
            self.update_level_visualization()
        
        # Schedule next update
        self.root.after(100, self.update_ui)
    
    def update_level_visualization(self):
        """Update the audio level visualization"""
        # Clear canvas
        self.level_canvas.delete("all")
        
        # Get canvas dimensions
        width = self.level_canvas.winfo_width()
        height = self.level_canvas.winfo_height()
        
        # Avoid division by zero
        if width < 10:
            width = 200  # Default width
        
        # Draw background
        self.level_canvas.create_rectangle(0, 0, width, height, fill="white")
        
        # Draw level bar
        level_width = int(width * min(1.0, self.audio_level / 100.0))
        
        # Color based on level
        if self.audio_level < 30:
            color = "green"
        elif self.audio_level < 70:
            color = "yellow"
        else:
            color = "red"
        
        self.level_canvas.create_rectangle(0, 0, level_width, height, fill=color)
        
        # Update level label
        self.level_label.config(text=f"Level: {self.audio_level:.1f}%")
    
    def toggle_recording(self):
        """Start or stop recording"""
        if not self.is_recording:
            self.start_recording()
        else:
            self.stop_recording()
    
    def start_recording(self):
        """Start recording audio"""
        if not AUDIO_AVAILABLE:
            messagebox.showerror("Error", "PyAudio is not available. Please install it to use audio recording.")
            return
        
        try:
            # Get selected device
            selected_index = self.device_dropdown.current()
            if selected_index < 0:
                messagebox.showerror("Error", "Please select an audio device")
                return
            
            # Clear previous recording
            self.audio_data = []
            self.audio_level = 0
            self.transcription_text.delete(1.0, tk.END)
            
            # Initialize PyAudio
            self.p = pyaudio.PyAudio()
            
            # Open stream
            self.stream = self.p.open(
                format=self.format,
                channels=self.channels,
                rate=self.sample_rate,
                input=True,
                input_device_index=self.selected_device_index,
                frames_per_buffer=self.chunk_size
            )
            
            # Update state and UI
            self.is_recording = True
            self.recording_start_time = time.time()
            self.record_button.config(text="Stop Recording")
            self.transcribe_button.config(state=tk.DISABLED)
            self.save_button.config(state=tk.DISABLED)
            self.save_transcript_button.config(state=tk.DISABLED)
            self.status_bar.config(text="Recording...")
            
            # Start recording thread
            self.recording_thread = threading.Thread(target=self._record_audio)
            self.recording_thread.daemon = True
            self.recording_thread.start()
            
            logger.info("Started recording")
            
        except Exception as e:
            logger.error(f"Error starting recording: {str(e)}")
            messagebox.showerror("Error", f"Failed to start recording: {str(e)}")
            self.cleanup()
    
    def _record_audio(self):
        """Record audio in a separate thread"""
        try:
            while self.is_recording and self.stream:
                try:
                    # Read audio data
                    data = self.stream.read(self.chunk_size, exception_on_overflow=False)
                    self.audio_data.append(data)
                    
                    # Calculate audio level
                    audio_array = np.frombuffer(data, dtype=np.int16)
                    max_value = np.max(np.abs(audio_array))
                    # Convert to percentage (0-100)
                    self.audio_level = min(100, (max_value / 32767) * 100)
                    
                except Exception as e:
                    logger.error(f"Error reading audio: {str(e)}")
                    time.sleep(0.1)  # Avoid tight loop on errors
        except Exception as e:
            logger.error(f"Error in recording thread: {str(e)}")
        finally:
            logger.info("Recording thread finished")
    
    def stop_recording(self):
        """Stop recording audio"""
        if not self.is_recording:
            return
        
        try:
            # Signal recording thread to stop
            self.is_recording = False
            
            # Wait for recording thread to finish (with timeout)
            if self.recording_thread and self.recording_thread.is_alive():
                self.recording_thread.join(timeout=1.0)
            
            # Close stream
            if self.stream:
                try:
                    self.stream.stop_stream()
                    self.stream.close()
                except Exception as e:
                    logger.error(f"Error closing stream: {e}")
                finally:
                    self.stream = None
            
            # Terminate PyAudio
            if self.p:
                try:
                    self.p.terminate()
                except Exception as e:
                    logger.error(f"Error terminating PyAudio: {e}")
                finally:
                    self.p = None
            
            # Update UI
            self.record_button.config(text="Start Recording")
            self.transcribe_button.config(state=tk.NORMAL if self.audio_data else tk.DISABLED)
            self.save_button.config(state=tk.NORMAL if self.audio_data else tk.DISABLED)
            self.status_bar.config(text="Recording stopped")
            
            logger.info("Stopped recording")
            
        except Exception as e:
            logger.error(f"Error stopping recording: {str(e)}")
            messagebox.showerror("Error", f"Error stopping recording: {str(e)}")
    
    def transcribe_recording(self):
        """Transcribe the recorded audio using OpenAI"""
        if not self.audio_data:
            messagebox.showinfo("Info", "No audio data to transcribe")
            return
        
        try:
            # Save to temporary file
            temp_dir = os.path.join(tempfile.gettempdir(), "SumOnTheFly")
            os.makedirs(temp_dir, exist_ok=True)
            
            self.temp_file = os.path.join(temp_dir, f"recording_{int(time.time())}.wav")
            
            # Save as WAV file
            with wave.open(self.temp_file, 'wb') as wf:
                wf.setnchannels(self.channels)
                wf.setsampwidth(2)  # 2 bytes for paInt16
                wf.setframerate(self.sample_rate)
                wf.writeframes(b''.join(self.audio_data))
            
            # Update status
            self.status_bar.config(text="Transcribing audio...")
            
            # Start transcription in a separate thread
            threading.Thread(
                target=self._transcribe_file,
                args=(self.temp_file,),
                daemon=True
            ).start()
            
        except Exception as e:
            logger.error(f"Error preparing for transcription: {str(e)}")
            messagebox.showerror("Error", f"Failed to prepare for transcription: {str(e)}")
    
    def _transcribe_file(self, file_path):
        """Transcribe audio file using OpenAI"""
        try:
            logger.info(f"Transcribing file: {file_path}")
            
            # Use the transcribe function
            result = transcribe_audio_file(file_path)
            
            # Update UI on the main thread
            self.root.after(0, self._update_transcription_ui, result)
            
            # Clean up temp file
            try:
                os.unlink(file_path)
                self.temp_file = None
            except Exception as e:
                logger.warning(f"Failed to delete temp file: {str(e)}")
                
        except Exception as e:
            error_msg = f"Error in transcription: {str(e)}"
            logger.error(error_msg)
            self.root.after(0, self._show_error, error_msg)
    
    def _update_transcription_ui(self, text):
        """Update the transcription UI"""
        self.transcription_text.delete(1.0, tk.END)
        self.transcription_text.insert(tk.END, text)
        self.status_bar.config(text="Transcription complete")
        self.save_transcript_button.config(state=tk.NORMAL)
    
    def save_transcript(self):
        """Save the transcription to a text file"""
        text = self.transcription_text.get(1.0, tk.END).strip()
        
        if not text:
            messagebox.showinfo("Info", "No text to save")
            return
        
        try:
            # Ask for save location
            filename = filedialog.asksaveasfilename(
                defaultextension=".txt",
                filetypes=[("Text files", "*.txt"), ("All files", "*.*")]
            )
            
            if not filename:
                return
            
            # Save as text file
            with open(filename, 'w') as f:
                f.write(text)
            
            self.status_bar.config(text=f"Transcript saved to {os.path.basename(filename)}")
            logger.info(f"Saved transcript to {filename}")
            
            messagebox.showinfo("Success", f"Transcript saved to {filename}")
            
        except Exception as e:
            logger.error(f"Error saving transcript: {str(e)}")
            messagebox.showerror("Error", f"Failed to save transcript: {str(e)}")
    
    def save_recording(self):
        """Save the recorded audio to a file"""
        if not self.audio_data:
            messagebox.showinfo("Info", "No audio data to save")
            return
        
        try:
            # Ask for save location
            filename = filedialog.asksaveasfilename(
                defaultextension=".wav",
                filetypes=[("WAV files", "*.wav"), ("All files", "*.*")]
            )
            
            if not filename:
                return
            
            # Save as WAV file
            with wave.open(filename, 'wb') as wf:
                wf.setnchannels(self.channels)
                wf.setsampwidth(2)  # 2 bytes for paInt16
                wf.setframerate(self.sample_rate)
                wf.writeframes(b''.join(self.audio_data))
            
            self.status_bar.config(text=f"Saved to {os.path.basename(filename)}")
            logger.info(f"Saved recording to {filename}")
            
            messagebox.showinfo("Success", f"Recording saved to {filename}")
            
        except Exception as e:
            logger.error(f"Error saving recording: {str(e)}")
            messagebox.showerror("Error", f"Failed to save recording: {str(e)}")
    
    def _show_error(self, message):
        """Show error message"""
        self.status_bar.config(text=message)
        messagebox.showerror("Error", message)
    
    def cleanup(self):
        """Clean up resources"""
        self.is_recording = False
        
        if self.stream:
            try:
                self.stream.stop_stream()
                self.stream.close()
            except:
                pass
            finally:
                self.stream = None
        
        if self.p:
            try:
                self.p.terminate()
            except:
                pass
            finally:
                self.p = None
        
        # Clean up temp file
        if self.temp_file and os.path.exists(self.temp_file):
            try:
                os.unlink(self.temp_file)
            except:
                pass

def main():
    """Main entry point for the application"""
    try:
        # Create the root window
        root = tk.Tk()
        
        # Create and run the application
        app = OpenAITranscriber(root)
        
        # Handle window close
        def on_closing():
            if app.is_recording:
                if messagebox.askyesno("Confirm", "Recording is in progress. Stop recording and exit?"):
                    app.stop_recording()
                    root.destroy()
            else:
                root.destroy()
        
        root.protocol("WM_DELETE_WINDOW", on_closing)
        root.mainloop()
        
    except Exception as e:
        logger.critical(f"Unhandled exception: {str(e)}")
        messagebox.showerror("Critical Error", f"An unhandled error occurred: {str(e)}")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
