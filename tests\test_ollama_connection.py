#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
import sys
import logging

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

def test_ollama_connection(host="http://CarlsMacStudio.got.volvo.net:11434"):
    """Test connection to Ollama server"""
    try:
        # Test basic connection
        logger.info(f"Testing connection to Ollama server at {host}")
        response = requests.get(f"{host}/api/version")
        
        logger.info(f"Response status: {response.status_code}")
        if response.status_code == 200:
            version = response.json().get("version", "unknown")
            logger.info(f"Connected to Ollama server (version {version})")
            
            # Test model list
            logger.info("Fetching available models...")
            models_response = requests.get(f"{host}/api/tags")
            
            if models_response.status_code == 200:
                models = models_response.json().get("models", [])
                logger.info(f"Available models: {[model.get('name') for model in models]}")
            else:
                logger.warning(f"Failed to get models: {models_response.status_code} - {models_response.text}")
            
            # Test audio endpoint
            logger.info("Testing audio endpoint...")
            audio_response = requests.get(f"{host}/api/audio")
            logger.info(f"Audio endpoint response: {audio_response.status_code}")
            
            # Try alternative audio endpoint
            logger.info("Testing alternative audio endpoint...")
            alt_audio_response = requests.get(f"{host}/api/generate")
            logger.info(f"Alternative endpoint response: {alt_audio_response.status_code}")
            
            return True
        else:
            logger.warning(f"Failed to connect to Ollama server: {response.status_code} - {response.text}")
            return False
    except Exception as e:
        logger.error(f"Error connecting to Ollama server: {str(e)}")
        return False

def main():
    if len(sys.argv) > 1:
        host = sys.argv[1]
    else:
        host = "http://CarlsMacStudio.got.volvo.net:11434"
    
    success = test_ollama_connection(host)
    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main())
