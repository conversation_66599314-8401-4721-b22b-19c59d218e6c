#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import sys
import shutil
import subprocess
import time
from pathlib import Path

def build_executable():
    """Build the full SumOnTheFly executable with all dependencies"""
    print("Building full SumOnTheFly executable...")
    
    # Clean any previous build directories
    for dir_name in ['build', 'dist']:
        if os.path.exists(dir_name):
            print(f"Cleaning {dir_name} directory...")
            try:
                shutil.rmtree(dir_name)
            except PermissionError as e:
                print(f"Warning: Could not remove {dir_name} directory: {e}")
                print(f"The executable might be in use. Please close it and try again.")
                # Try to rename the dist directory instead
                if dir_name == 'dist':
                    try:
                        backup_dir = f"{dir_name}_old_{int(time.time())}"
                        print(f"Renaming {dir_name} to {backup_dir}")
                        os.rename(dir_name, backup_dir)
                    except Exception as rename_err:
                        print(f"Could not rename directory: {rename_err}")
                        print("Will attempt to build anyway...")
    
    # PyInstaller command with all necessary options
    cmd = [
        'pyinstaller',
        '--name=SumOnTheFly_Full',
        '--onefile',  # Create a single executable file
        '--windowed',  # Don't show console window on Windows
        '--add-data=README.md;.',  # Include README file
        
        # Add hidden imports for core functionality
        '--hidden-import=numpy',
        '--hidden-import=tkinter',
        '--hidden-import=tkinter.filedialog',
        '--hidden-import=tkinter.messagebox',
        '--hidden-import=tkinter.ttk',
        '--hidden-import=threading',
        '--hidden-import=time',
        '--hidden-import=datetime',
        '--hidden-import=json',
        '--hidden-import=logging',
        '--hidden-import=sqlite3',
        
        # Add audio-related imports
        '--hidden-import=pyaudio',
        '--hidden-import=sounddevice',
        '--hidden-import=wave',
        
        # Add export-related imports
        '--hidden-import=reportlab',
        '--hidden-import=reportlab.lib.pagesizes',
        '--hidden-import=reportlab.lib.styles',
        '--hidden-import=reportlab.platypus',
        '--hidden-import=docx',
        
        # Main script
        'app.py'
    ]
    
    # Execute PyInstaller
    print("Running PyInstaller...")
    print(f"Command: {' '.join(cmd)}")
    result = subprocess.run(cmd, capture_output=False)  # Show output in real-time
    
    if result.returncode != 0:
        print("Error building executable")
        return False
    
    print("PyInstaller completed successfully.")
    
    # Check if executable was created
    exe_path = os.path.join('dist', 'SumOnTheFly_Full.exe')
    if os.path.exists(exe_path):
        print(f"Executable created at: {os.path.abspath(exe_path)}")
        return True
    else:
        print("Executable was not created. Check the PyInstaller output.")
        return False

if __name__ == "__main__":
    success = build_executable()
    sys.exit(0 if success else 1)
