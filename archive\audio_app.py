#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import logging
import threading
import time
import numpy as np
import os
import sys
import tkinter as tk
from tkinter import ttk, messagebox, filedialog
from datetime import datetime

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("audio_app.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# Check for audio libraries
try:
    import pyaudio
    PYAUDIO_AVAILABLE = True
    logger.info("PyAudio is available")
except ImportError:
    PYAUDIO_AVAILABLE = False
    logger.warning("PyAudio is not available")

try:
    import wave
    WAVE_AVAILABLE = True
    logger.info("wave module is available")
except ImportError:
    WAVE_AVAILABLE = False
    logger.warning("wave module is not available")

class AudioRecorder:
    """Audio recording class with visualization"""
    
    def __init__(self, 
                 sample_rate=16000, 
                 chunk_size=1024,
                 channels=1):
        self.sample_rate = sample_rate
        self.chunk_size = chunk_size
        self.channels = channels
        self.is_recording = False
        self.audio_buffer = []
        self.lock = threading.Lock()
        self.recording_thread = None
        self.pyaudio_instance = None
        self.device_index = None
        
        # Initialize PyAudio if available
        if PYAUDIO_AVAILABLE:
            try:
                self.pyaudio_instance = pyaudio.PyAudio()
                self._log_available_devices()
            except Exception as e:
                logger.error(f"Failed to initialize PyAudio: {str(e)}")
    
    def _log_available_devices(self):
        """Log information about available audio devices"""
        if not PYAUDIO_AVAILABLE or not self.pyaudio_instance:
            return
            
        try:
            logger.info(f"Available audio devices:")
            device_count = self.pyaudio_instance.get_device_count()
            
            try:
                default_input = self.pyaudio_instance.get_default_input_device_info()
                logger.info(f"Default input device: {default_input['name']} (index: {default_input['index']})")
                self.device_index = default_input['index']
            except Exception as e:
                logger.warning(f"Error getting default input device: {str(e)}")
                self.device_index = None
            
            for i in range(device_count):
                try:
                    device_info = self.pyaudio_instance.get_device_info_by_index(i)
                    input_channels = device_info.get('maxInputChannels', 0)
                    if input_channels > 0:
                        logger.info(f"Device {i}: {device_info.get('name')} (Input Channels: {input_channels})")
                        # If no default device was found, use the first one with input channels
                        if self.device_index is None:
                            self.device_index = i
                            logger.info(f"Using device {i} as fallback")
                except Exception as e:
                    logger.warning(f"Error getting info for device {i}: {str(e)}")
        except Exception as e:
            logger.error(f"Error logging audio devices: {str(e)}")
    
    def get_available_devices(self):
        """Get a list of available audio input devices"""
        devices = []
        
        if PYAUDIO_AVAILABLE and self.pyaudio_instance:
            try:
                device_count = self.pyaudio_instance.get_device_count()
                for i in range(device_count):
                    try:
                        device_info = self.pyaudio_instance.get_device_info_by_index(i)
                        if device_info.get('maxInputChannels', 0) > 0:
                            devices.append({
                                'index': i,
                                'name': device_info.get('name', f"Device {i}"),
                                'channels': device_info.get('maxInputChannels', 0),
                                'default': False
                            })
                    except Exception as e:
                        logger.warning(f"Error getting info for device {i}: {str(e)}")
            except Exception as e:
                logger.error(f"Error getting audio devices: {str(e)}")
        
        # Mark default device
        if self.device_index is not None:
            for device in devices:
                if device['index'] == self.device_index:
                    device['default'] = True
                    break
        
        return devices
    
    def select_device(self, device_index):
        """Select a specific audio device"""
        self.device_index = device_index
        logger.info(f"Selected device index: {device_index}")
    
    def start_recording(self):
        """Start recording audio"""
        if self.is_recording:
            logger.warning("Recording is already in progress")
            return False
        
        if not PYAUDIO_AVAILABLE or not self.pyaudio_instance:
            logger.error("PyAudio is not available")
            return False
        
        try:
            self.is_recording = True
            self.audio_buffer = []
            
            # Start recording thread
            self.recording_thread = threading.Thread(
                target=self._record_audio,
                daemon=True
            )
            self.recording_thread.start()
            logger.info("Started recording audio")
            
            return True
        except Exception as e:
            logger.error(f"Failed to start recording: {str(e)}")
            self.is_recording = False
            return False
    
    def stop_recording(self):
        """Stop recording audio"""
        if not self.is_recording:
            logger.warning("No recording in progress")
            return False
        
        self.is_recording = False
        
        # Wait for thread to finish
        if self.recording_thread and self.recording_thread.is_alive():
            self.recording_thread.join(timeout=2.0)
        
        logger.info("Stopped recording audio")
        return True
    
    def _record_audio(self):
        """Record audio from the selected device"""
        try:
            # Open audio stream
            stream = self.pyaudio_instance.open(
                format=pyaudio.paInt16,
                channels=self.channels,
                rate=self.sample_rate,
                input=True,
                input_device_index=self.device_index,
                frames_per_buffer=self.chunk_size
            )
            
            logger.info(f"Audio stream opened successfully with device index {self.device_index}")
            
            while self.is_recording:
                try:
                    audio_data = stream.read(self.chunk_size, exception_on_overflow=False)
                    audio_array = np.frombuffer(audio_data, dtype=np.int16)
                    
                    # Calculate audio level for visualization
                    level = np.abs(audio_array).mean()
                    logger.debug(f"Audio level: {level:.2f}")
                    
                    with self.lock:
                        self.audio_buffer.append(audio_data)
                except Exception as e:
                    logger.warning(f"Error reading from microphone: {str(e)}")
                    time.sleep(0.1)  # Avoid tight loop if there are persistent errors
            
            # Clean up
            try:
                stream.stop_stream()
                stream.close()
            except Exception as e:
                logger.warning(f"Error closing audio stream: {str(e)}")
                
        except Exception as e:
            logger.error(f"Error in audio recording: {str(e)}")
            self.is_recording = False
    
    def save_to_file(self, filename):
        """Save recorded audio to a WAV file"""
        if not WAVE_AVAILABLE:
            logger.error("wave module is not available. Cannot save audio to file.")
            return False
            
        try:
            with self.lock:
                if not self.audio_buffer:
                    logger.warning("No audio data to save")
                    return False
                
                with wave.open(filename, 'wb') as wf:
                    wf.setnchannels(self.channels)
                    wf.setsampwidth(self.pyaudio_instance.get_sample_size(pyaudio.paInt16))
                    wf.setframerate(self.sample_rate)
                    wf.writeframes(b''.join(self.audio_buffer))
            
            logger.info(f"Saved audio to {filename}")
            return True
        except Exception as e:
            logger.error(f"Failed to save audio to file: {str(e)}")
            return False
    
    def get_audio_level(self):
        """Get the current audio level for visualization"""
        with self.lock:
            if not self.audio_buffer or len(self.audio_buffer) == 0:
                return 0
            
            # Get the most recent audio chunk
            latest_chunk = self.audio_buffer[-1]
            audio_array = np.frombuffer(latest_chunk, dtype=np.int16)
            level = np.abs(audio_array).mean()
            
            # Normalize to 0-100 range
            normalized_level = min(100, level / 50)  # Adjust divisor as needed
            return normalized_level
    
    def __del__(self):
        """Clean up resources"""
        self.stop_recording()
        if PYAUDIO_AVAILABLE and hasattr(self, 'pyaudio_instance') and self.pyaudio_instance:
            self.pyaudio_instance.terminate()

class AudioRecorderApp:
    """Simple audio recorder application"""
    
    def __init__(self, root):
        self.root = root
        self.root.title("SumOnTheFly Audio Recorder")
        self.root.geometry("600x400")
        self.root.minsize(500, 300)
        
        # Initialize recorder
        self.recorder = AudioRecorder()
        self.is_recording = False
        self.recording_start_time = 0
        
        # Set up UI
        self.setup_ui()
        
        # Start UI update timer
        self.update_ui()
    
    def setup_ui(self):
        """Set up the UI components"""
        # Main frame
        self.main_frame = ttk.Frame(self.root, padding=10)
        self.main_frame.pack(fill=tk.BOTH, expand=True)
        
        # Device selection
        device_frame = ttk.LabelFrame(self.main_frame, text="Audio Device")
        device_frame.pack(fill=tk.X, pady=5)
        
        # Get available devices
        devices = self.recorder.get_available_devices()
        device_names = [f"{d['index']}: {d['name']}" + (" (Default)" if d['default'] else "") for d in devices]
        
        if not device_names:
            device_names = ["No audio devices found"]
        
        self.device_var = tk.StringVar(value=device_names[0] if device_names else "")
        self.device_dropdown = ttk.Combobox(device_frame, textvariable=self.device_var, values=device_names, state="readonly")
        self.device_dropdown.pack(fill=tk.X, padx=5, pady=5)
        self.device_dropdown.bind("<<ComboboxSelected>>", self.on_device_selected)
        
        # Control panel
        control_frame = ttk.Frame(self.main_frame)
        control_frame.pack(fill=tk.X, pady=10)
        
        self.record_button = ttk.Button(
            control_frame, 
            text="Start Recording", 
            command=self.toggle_recording
        )
        self.record_button.pack(side=tk.LEFT, padx=5)
        
        self.save_button = ttk.Button(
            control_frame, 
            text="Save Recording", 
            command=self.save_recording,
            state=tk.DISABLED
        )
        self.save_button.pack(side=tk.LEFT, padx=5)
        
        self.time_label = ttk.Label(control_frame, text="00:00:00")
        self.time_label.pack(side=tk.RIGHT, padx=5)
        
        # Audio level visualization
        level_frame = ttk.LabelFrame(self.main_frame, text="Audio Level")
        level_frame.pack(fill=tk.X, pady=10)
        
        self.level_canvas = tk.Canvas(level_frame, height=30, bg="white")
        self.level_canvas.pack(fill=tk.X, padx=5, pady=5)
        
        # Status bar
        self.status_bar = ttk.Label(
            self.root, 
            text="Ready", 
            relief=tk.SUNKEN, 
            anchor=tk.W
        )
        self.status_bar.pack(side=tk.BOTTOM, fill=tk.X)
    
    def on_device_selected(self, event):
        """Handle device selection"""
        if self.is_recording:
            messagebox.showwarning("Warning", "Please stop recording before changing the device")
            return
        
        selected = self.device_dropdown.get()
        if selected and ":" in selected:
            device_index = int(selected.split(":")[0])
            self.recorder.select_device(device_index)
            self.status_bar.config(text=f"Selected device: {selected}")
    
    def toggle_recording(self):
        """Start or stop recording"""
        if not self.is_recording:
            self.start_recording()
        else:
            self.stop_recording()
    
    def start_recording(self):
        """Start recording audio"""
        if self.recorder.start_recording():
            self.is_recording = True
            self.recording_start_time = time.time()
            self.record_button.config(text="Stop Recording")
            self.save_button.config(state=tk.DISABLED)
            self.status_bar.config(text="Recording...")
        else:
            messagebox.showerror("Error", "Failed to start recording")
    
    def stop_recording(self):
        """Stop recording audio"""
        if self.recorder.stop_recording():
            self.is_recording = False
            self.record_button.config(text="Start Recording")
            self.save_button.config(state=tk.NORMAL)
            self.status_bar.config(text="Recording stopped")
    
    def save_recording(self):
        """Save the recorded audio to a file"""
        if self.is_recording:
            messagebox.showwarning("Warning", "Please stop recording before saving")
            return
        
        filename = filedialog.asksaveasfilename(
            defaultextension=".wav",
            filetypes=[("WAV files", "*.wav")],
            initialfile=f"recording_{datetime.now().strftime('%Y%m%d_%H%M%S')}.wav"
        )
        
        if filename:
            if self.recorder.save_to_file(filename):
                messagebox.showinfo("Success", f"Recording saved to {filename}")
            else:
                messagebox.showerror("Error", "Failed to save recording")
    
    def update_ui(self):
        """Update the UI periodically"""
        # Update recording time
        if self.is_recording:
            elapsed = time.time() - self.recording_start_time
            hours, remainder = divmod(int(elapsed), 3600)
            minutes, seconds = divmod(remainder, 60)
            self.time_label.config(text=f"{hours:02d}:{minutes:02d}:{seconds:02d}")
        
        # Update audio level visualization
        if self.is_recording:
            level = self.recorder.get_audio_level()
            self.update_level_visualization(level)
        
        # Schedule the next update
        self.root.after(100, self.update_ui)
    
    def update_level_visualization(self, level):
        """Update the audio level visualization"""
        self.level_canvas.delete("all")
        width = self.level_canvas.winfo_width()
        height = self.level_canvas.winfo_height()
        
        # Draw background
        self.level_canvas.create_rectangle(0, 0, width, height, fill="white")
        
        # Draw level bar
        bar_width = int(width * (level / 100))
        
        # Color based on level
        if level < 30:
            color = "green"
        elif level < 70:
            color = "yellow"
        else:
            color = "red"
        
        self.level_canvas.create_rectangle(0, 0, bar_width, height, fill=color)

def main():
    """Main entry point for the application"""
    try:
        # Create the root window
        root = tk.Tk()
        
        # Create and run the application
        app = AudioRecorderApp(root)
        root.mainloop()
        
    except Exception as e:
        logger.critical(f"Unhandled exception: {str(e)}")
        messagebox.showerror("Critical Error", f"An unhandled error occurred: {str(e)}")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
