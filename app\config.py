import os
import json
import logging

logger = logging.getLogger(__name__)

class Config:
    """Configuration manager for the application"""
    
    def __init__(self, config_file=None):
        """Initialize the configuration manager"""
        # Default configuration file location
        if config_file is None:
            config_dir = os.path.join(os.path.expanduser("~"), ".sumonthefly")
            os.makedirs(config_dir, exist_ok=True)
            config_file = os.path.join(config_dir, "config.json")
        
        self.config_file = config_file
        self.config = self._load_config()
    
    def _load_config(self):
        """Load configuration from file or create default"""
        if os.path.exists(self.config_file):
            try:
                with open(self.config_file, 'r') as f:
                    return json.load(f)
            except Exception as e:
                logger.error(f"Error loading config file: {str(e)}")
                return self._get_default_config()
        else:
            return self._get_default_config()
    
    def _get_default_config(self):
        """Get default configuration settings"""
        return {
            # General settings
            "summary_interval": 30,  # seconds
            "language": "English",
            "auto_start": False,
            "data_dir": os.path.join(os.path.expanduser("~"), "SumOnTheFly"),
            
            # Ollama settings
            "ollama_host": "http://CarlsMacStudio.got.volvo.net:11434",
            "transcription_model": "dimavz/whisper-tiny",
            "summarization_model": "qwen3",
            "model_params": {
                "temperature": 0.7,
                "top_p": 0.9,
                "max_tokens": 500
            },
            
            # Audio settings
            "sample_rate": 16000,
            "channels": 1,
            "chunk_size": 1024,
            "input_device_index": 0,
            "input_device_name": "Default"
        }
    
    def get(self, key, default=None):
        """Get a configuration value"""
        return self.config.get(key, default)
    
    def set(self, key, value):
        """Set a configuration value"""
        self.config[key] = value
    
    def save(self):
        """Save configuration to file"""
        try:
            # Ensure directory exists
            os.makedirs(os.path.dirname(self.config_file), exist_ok=True)
            
            with open(self.config_file, 'w') as f:
                json.dump(self.config, f, indent=4)
            
            logger.info(f"Configuration saved to {self.config_file}")
            return True
        except Exception as e:
            logger.error(f"Error saving configuration: {str(e)}")
            return False


# Example usage
if __name__ == "__main__":
    config = Config()
    print(f"Loaded configuration from {config.config_file}")
    print(f"Summary interval: {config.get('summary_interval')} seconds")
    
    # Example of changing a setting
    config.set("summary_interval", 45)
    config.save()
