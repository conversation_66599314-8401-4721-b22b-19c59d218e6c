#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SumOnTheFly - Main entry point
A desktop application that records audio, transcribes it in real-time, and generates summaries.
"""

import sys
import os

# Add the project root to Python path
project_root = os.path.dirname(os.path.abspath(__file__))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

# Import and run the main application
from app.main import main

if __name__ == "__main__":
    sys.exit(main())
