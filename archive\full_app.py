#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import sys
import logging
import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import threading
import time
from datetime import datetime
import json
import numpy as np

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("sumonthefly.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# Check for audio libraries
try:
    import pyaudio
    PYAUDIO_AVAILABLE = True
    logger.info("PyAudio is available")
except ImportError:
    PYAUDIO_AVAILABLE = False
    logger.warning("PyAudio is not available")

# Import backend modules
try:
    from backend.audio_capture import AudioCapture
    from backend.transcription import TranscriptionService
    from backend.summarization import SummarizationService
    from backend.database import DatabaseManager
    from backend.export import ExportService
    from config import Config
except ImportError as e:
    logger.critical(f"Failed to import required modules: {str(e)}")
    sys.exit(1)

class SumOnTheFlyApp:
    """Main application class for SumOnTheFly"""
    
    def __init__(self, root):
        self.root = root
        self.root.title("SumOnTheFly")
        self.root.geometry("1024x768")
        self.root.minsize(800, 600)
        
        # Initialize components
        self.config = Config()
        self.db = DatabaseManager()
        self.audio_capture = None
        self.transcription_service = None
        self.summarization_service = None
        self.export_service = ExportService()
        
        # State variables
        self.is_recording = False
        self.current_session_id = None
        self.transcription_buffer = ""
        self.last_summary_time = 0
        self.recording_start_time = 0
        self.audio_devices = []
        self.selected_device_index = None
        
        # Set up UI
        self.setup_ui()
        
        # Detect audio devices
        self.detect_audio_devices()
        
        # Start UI update timer
        self.update_ui()
    
    def detect_audio_devices(self):
        """Detect available audio input devices"""
        self.audio_devices = []
        self.selected_device_index = None
        
        if PYAUDIO_AVAILABLE:
            try:
                p = pyaudio.PyAudio()
                
                # Try to get default input device
                try:
                    default_device = p.get_default_input_device_info()
                    logger.info(f"Default input device: {default_device['name']} (index: {default_device['index']})")
                    self.selected_device_index = default_device['index']
                except Exception as e:
                    logger.warning(f"Error getting default input device: {str(e)}")
                
                # Get all input devices
                device_count = p.get_device_count()
                for i in range(device_count):
                    try:
                        device_info = p.get_device_info_by_index(i)
                        if device_info.get('maxInputChannels', 0) > 0:
                            self.audio_devices.append({
                                'index': i,
                                'name': device_info.get('name', f"Device {i}"),
                                'channels': device_info.get('maxInputChannels', 0),
                                'default': i == self.selected_device_index
                            })
                            
                            # If no default device was found, use the first one with input channels
                            if self.selected_device_index is None:
                                self.selected_device_index = i
                                logger.info(f"Using device {i} as fallback")
                    except Exception as e:
                        logger.warning(f"Error getting info for device {i}: {str(e)}")
                
                # Clean up
                p.terminate()
                
                # Update device dropdown if it exists
                if hasattr(self, 'device_dropdown') and self.device_dropdown:
                    device_names = [f"{d['index']}: {d['name']}" + (" (Default)" if d['default'] else "") for d in self.audio_devices]
                    self.device_dropdown['values'] = device_names
                    if device_names:
                        self.device_var.set(device_names[0])
            except Exception as e:
                logger.error(f"Error detecting audio devices: {str(e)}")
    
    def setup_ui(self):
        """Set up the main UI components"""
        # Main frame
        self.main_frame = ttk.Frame(self.root, padding=10)
        self.main_frame.pack(fill=tk.BOTH, expand=True)
        
        # Create menu
        self.create_menu()
        
        # Audio device selection
        device_frame = ttk.LabelFrame(self.main_frame, text="Audio Device")
        device_frame.pack(fill=tk.X, pady=5)
        
        self.device_var = tk.StringVar()
        self.device_dropdown = ttk.Combobox(device_frame, textvariable=self.device_var, state="readonly")
        self.device_dropdown.pack(side=tk.LEFT, padx=5, pady=5, fill=tk.X, expand=True)
        self.device_dropdown.bind("<<ComboboxSelected>>", self.on_device_selected)
        
        refresh_button = ttk.Button(device_frame, text="Refresh", command=self.detect_audio_devices)
        refresh_button.pack(side=tk.RIGHT, padx=5, pady=5)
        
        # Control panel
        control_frame = ttk.LabelFrame(self.main_frame, text="Controls")
        control_frame.pack(fill=tk.X, pady=5)
        
        self.record_button = ttk.Button(
            control_frame, 
            text="Start Recording", 
            command=self.toggle_recording
        )
        self.record_button.pack(side=tk.LEFT, padx=5, pady=5)
        
        self.session_label = ttk.Label(control_frame, text="No active session")
        self.session_label.pack(side=tk.LEFT, padx=5, pady=5)
        
        self.time_label = ttk.Label(control_frame, text="00:00:00")
        self.time_label.pack(side=tk.RIGHT, padx=5, pady=5)
        
        # Audio level visualization
        level_frame = ttk.Frame(control_frame)
        level_frame.pack(side=tk.RIGHT, padx=5, pady=5, fill=tk.X, expand=True)
        
        ttk.Label(level_frame, text="Audio Level:").pack(side=tk.LEFT)
        
        self.level_canvas = tk.Canvas(level_frame, width=100, height=20, bg="white")
        self.level_canvas.pack(side=tk.LEFT, padx=5, fill=tk.X, expand=True)
        
        # Transcription panel
        transcription_frame = ttk.LabelFrame(self.main_frame, text="Real-time Transcription")
        transcription_frame.pack(fill=tk.BOTH, expand=True, pady=5)
        
        self.transcription_text = tk.Text(
            transcription_frame,
            wrap=tk.WORD,
            state=tk.DISABLED
        )
        transcription_scroll = ttk.Scrollbar(
            transcription_frame, 
            command=self.transcription_text.yview
        )
        self.transcription_text.configure(yscrollcommand=transcription_scroll.set)
        
        self.transcription_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        transcription_scroll.pack(side=tk.RIGHT, fill=tk.Y)
        
        # Summary panel
        summary_frame = ttk.LabelFrame(self.main_frame, text="Summaries")
        summary_frame.pack(fill=tk.BOTH, expand=True, pady=5)
        
        self.summary_text = tk.Text(
            summary_frame,
            wrap=tk.WORD,
            state=tk.DISABLED,
            height=10
        )
        summary_scroll = ttk.Scrollbar(
            summary_frame, 
            command=self.summary_text.yview
        )
        self.summary_text.configure(yscrollcommand=summary_scroll.set)
        
        self.summary_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        summary_scroll.pack(side=tk.RIGHT, fill=tk.Y)
        
        # Status bar
        self.status_bar = ttk.Label(
            self.root, 
            text="Ready", 
            relief=tk.SUNKEN, 
            anchor=tk.W
        )
        self.status_bar.pack(side=tk.BOTTOM, fill=tk.X)
    
    def on_device_selected(self, event):
        """Handle device selection"""
        if self.is_recording:
            messagebox.showwarning("Warning", "Please stop recording before changing the device")
            return
        
        selected = self.device_dropdown.get()
        if selected and ":" in selected:
            device_index = int(selected.split(":")[0])
            self.selected_device_index = device_index
            self.status_bar.config(text=f"Selected device: {selected}")
    
    def create_menu(self):
        """Create the application menu"""
        menubar = tk.Menu(self.root)
        
        # File menu
        file_menu = tk.Menu(menubar, tearoff=0)
        file_menu.add_command(label="New Session", command=self.new_session)
        file_menu.add_separator()
        file_menu.add_command(label="Export Session", command=self.export_session)
        file_menu.add_separator()
        file_menu.add_command(label="Exit", command=self.root.quit)
        menubar.add_cascade(label="File", menu=file_menu)
        
        # Settings menu
        settings_menu = tk.Menu(menubar, tearoff=0)
        settings_menu.add_command(label="Configure", command=self.show_settings)
        menubar.add_cascade(label="Settings", menu=settings_menu)
        
        # Help menu
        help_menu = tk.Menu(menubar, tearoff=0)
        help_menu.add_command(label="About", command=self.show_about)
        menubar.add_cascade(label="Help", menu=help_menu)
        
        self.root.config(menu=menubar)
    
    def update_ui(self):
        """Periodically update the UI"""
        if self.is_recording:
            # Update recording time
            elapsed = time.time() - self.recording_start_time
            hours, remainder = divmod(int(elapsed), 3600)
            minutes, seconds = divmod(remainder, 60)
            self.time_label.config(text=f"{hours:02d}:{minutes:02d}:{seconds:02d}")
            
            # Update audio level visualization
            if self.audio_capture:
                level = self.get_audio_level()
                self.update_level_visualization(level)
            
            # Check if it's time for a new summary
            summary_interval = self.config.get("summary_interval", 30)
            if elapsed - self.last_summary_time >= summary_interval and self.transcription_buffer.strip():
                self.generate_summary()
                self.last_summary_time = elapsed
        
        # Schedule the next update
        self.root.after(100, self.update_ui)
    
    def get_audio_level(self):
        """Get the current audio level for visualization"""
        if not hasattr(self, 'recent_audio_chunks'):
            self.recent_audio_chunks = []
        
        if not self.recent_audio_chunks:
            return 0
        
        # Calculate average level from recent chunks
        levels = [np.abs(chunk).mean() for chunk in self.recent_audio_chunks]
        avg_level = sum(levels) / len(levels)
        
        # Normalize to 0-100 range
        normalized_level = min(100, avg_level / 50)  # Adjust divisor as needed
        return normalized_level
    
    def update_level_visualization(self, level):
        """Update the audio level visualization"""
        self.level_canvas.delete("all")
        width = self.level_canvas.winfo_width()
        height = self.level_canvas.winfo_height()
        
        # Draw background
        self.level_canvas.create_rectangle(0, 0, width, height, fill="white")
        
        # Draw level bar
        bar_width = int(width * (level / 100))
        
        # Color based on level
        if level < 30:
            color = "green"
        elif level < 70:
            color = "yellow"
        else:
            color = "red"
        
        self.level_canvas.create_rectangle(0, 0, bar_width, height, fill=color)
    
    def toggle_recording(self):
        """Start or stop recording"""
        if not self.is_recording:
            self.start_recording()
        else:
            self.stop_recording()
    
    def start_recording(self):
        """Start a new recording session"""
        try:
            # Check if a device is selected
            if self.selected_device_index is None:
                messagebox.showerror("Error", "No audio device selected")
                return
            
            # Create a new session
            session_name = f"Session_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            self.current_session_id = self.db.create_session(session_name)
            
            # Initialize services
            self.audio_capture = AudioCapture(
                sample_rate=self.config.get("sample_rate", 16000),
                channels=self.config.get("channels", 1),
                chunk_size=self.config.get("chunk_size", 1024),
                on_audio_chunk=self.on_audio_chunk,
                input_device_index=self.selected_device_index
            )
            
            self.transcription_service = TranscriptionService(
                ollama_host=self.config.get("ollama_host"),
                model=self.config.get("transcription_model")
            )
            
            self.summarization_service = SummarizationService(
                ollama_host=self.config.get("ollama_host"),
                model=self.config.get("summarization_model")
            )
            
            # Initialize audio level tracking
            self.recent_audio_chunks = []
            
            # Start recording
            success = self.audio_capture.start_recording()
            if not success:
                raise Exception("Failed to start audio recording")
            
            # Update UI
            self.is_recording = True
            self.recording_start_time = time.time()
            self.last_summary_time = 0
            self.transcription_buffer = ""
            self.record_button.config(text="Stop Recording")
            self.session_label.config(text=f"Active session: {session_name}")
            self.status_bar.config(text="Recording...")
            
            # Clear text areas
            self.transcription_text.config(state=tk.NORMAL)
            self.transcription_text.delete(1.0, tk.END)
            self.transcription_text.config(state=tk.DISABLED)
            
            self.summary_text.config(state=tk.NORMAL)
            self.summary_text.delete(1.0, tk.END)
            self.summary_text.config(state=tk.DISABLED)
            
            logger.info(f"Started recording session: {session_name}")
            
        except Exception as e:
            logger.error(f"Error starting recording: {str(e)}")
            messagebox.showerror("Error", f"Failed to start recording: {str(e)}")
            self.cleanup_services()
    
    def stop_recording(self):
        """Stop the current recording session"""
        if not self.is_recording:
            return
        
        try:
            # Stop services
            if self.audio_capture:
                self.audio_capture.stop_recording()
            
            # Generate final summary if needed
            if self.transcription_buffer.strip():
                self.generate_summary()
            
            # Update UI
            self.is_recording = False
            self.record_button.config(text="Start Recording")
            self.status_bar.config(text="Ready")
            
            # Save session data
            if self.current_session_id:
                self.db.update_session_end_time(self.current_session_id)
            
            logger.info("Stopped recording session")
            
        except Exception as e:
            logger.error(f"Error stopping recording: {str(e)}")
            messagebox.showerror("Error", f"Error stopping recording: {str(e)}")
        finally:
            self.cleanup_services()
    
    def cleanup_services(self):
        """Clean up and release resources"""
        self.audio_capture = None
        self.transcription_service = None
        self.summarization_service = None
        self.recent_audio_chunks = []
    
    def on_audio_chunk(self, audio_chunk):
        """Handle new audio chunk from the recorder"""
        # Keep track of recent chunks for level visualization
        self.recent_audio_chunks.append(audio_chunk)
        if len(self.recent_audio_chunks) > 5:  # Keep only the 5 most recent chunks
            self.recent_audio_chunks.pop(0)
        
        # Send to transcription service
        if self.transcription_service:
            self.transcription_service.process_audio(audio_chunk, self.on_transcription)
    
    def on_transcription(self, text):
        """Handle new transcription text"""
        if not text or not self.is_recording:
            return
        
        # Update the transcription buffer
        self.transcription_buffer += text + " "
        
        # Update the UI
        self.transcription_text.config(state=tk.NORMAL)
        self.transcription_text.insert(tk.END, text + " ")
        self.transcription_text.see(tk.END)
        self.transcription_text.config(state=tk.DISABLED)
        
        # Save to database
        if self.current_session_id:
            self.db.add_transcription(self.current_session_id, text, time.time())
    
    def generate_summary(self):
        """Generate a summary from the current transcription buffer"""
        if not self.summarization_service or not self.transcription_buffer.strip():
            return
        
        try:
            # Request summary asynchronously
            self.summarization_service.summarize_text(
                self.transcription_buffer,
                "Summarize the key points in 3-5 bullets.",
                self.on_summary
            )
            
            # Clear the transcription buffer for the next summary period
            self.transcription_buffer = ""
            
        except Exception as e:
            logger.error(f"Error generating summary: {str(e)}")
            self.status_bar.config(text=f"Error generating summary: {str(e)}")
    
    def on_summary(self, summary):
        """Handle new summary"""
        if not summary or not self.is_recording:
            return
        
        # Format the summary with timestamp
        timestamp = datetime.now().strftime("%H:%M:%S")
        formatted_summary = f"\n[{timestamp}]\n{summary}\n"
        
        # Update the UI
        self.summary_text.config(state=tk.NORMAL)
        self.summary_text.insert(tk.END, formatted_summary)
        self.summary_text.see(tk.END)
        self.summary_text.config(state=tk.DISABLED)
        
        # Save to database
        if self.current_session_id:
            self.db.add_summary(self.current_session_id, summary, time.time())
    
    def new_session(self):
        """Start a new session"""
        if self.is_recording:
            if messagebox.askyesno("Confirm", "Stop current recording and start a new session?"):
                self.stop_recording()
                self.start_recording()
        else:
            self.start_recording()
    
    def export_session(self):
        """Export the current session"""
        if not self.current_session_id:
            messagebox.showinfo("Info", "No active session to export")
            return
        
        try:
            # Get session data
            session_data = self.db.get_session_data(self.current_session_id)
            
            # Ask for save location and format
            filetypes = [("PDF files", "*.pdf"), ("Word files", "*.docx"), ("Text files", "*.txt")]
            filename = filedialog.asksaveasfilename(
                defaultextension=".pdf",
                filetypes=filetypes
            )
            
            if not filename:
                return
            
            # Export the file
            if self.export_service.export_session(filename, session_data):
                messagebox.showinfo("Success", f"Session exported to {filename}")
            else:
                messagebox.showerror("Error", "Failed to export session")
                
        except Exception as e:
            logger.error(f"Error exporting session: {str(e)}")
            messagebox.showerror("Error", f"Failed to export session: {str(e)}")
    
    def show_settings(self):
        """Show the settings dialog"""
        # Create a new top-level window
        settings_window = tk.Toplevel(self.root)
        settings_window.title("Settings")
        settings_window.geometry("500x400")
        settings_window.transient(self.root)  # Make it modal
        settings_window.grab_set()
        
        # Create a notebook for tabbed settings
        notebook = ttk.Notebook(settings_window)
        notebook.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # General settings tab
        general_frame = ttk.Frame(notebook, padding=10)
        notebook.add(general_frame, text="General")
        
        # Ollama settings tab
        ollama_frame = ttk.Frame(notebook, padding=10)
        notebook.add(ollama_frame, text="Ollama")
        
        # Audio settings tab
        audio_frame = ttk.Frame(notebook, padding=10)
        notebook.add(audio_frame, text="Audio")
        
        # General settings
        ttk.Label(general_frame, text="Summary Interval (seconds):").grid(row=0, column=0, sticky=tk.W, pady=5)
        summary_interval = tk.StringVar(value=str(self.config.get("summary_interval", 30)))
        ttk.Entry(general_frame, textvariable=summary_interval).grid(row=0, column=1, sticky=tk.W, pady=5)
        
        # Ollama settings
        ttk.Label(ollama_frame, text="Ollama Host:").grid(row=0, column=0, sticky=tk.W, pady=5)
        ollama_host = tk.StringVar(value=self.config.get("ollama_host", "http://localhost:11434"))
        ttk.Entry(ollama_frame, textvariable=ollama_host, width=40).grid(row=0, column=1, sticky=tk.W, pady=5)
        
        ttk.Label(ollama_frame, text="Transcription Model:").grid(row=1, column=0, sticky=tk.W, pady=5)
        transcription_model = tk.StringVar(value=self.config.get("transcription_model", "whisper"))
        ttk.Entry(ollama_frame, textvariable=transcription_model).grid(row=1, column=1, sticky=tk.W, pady=5)
        
        ttk.Label(ollama_frame, text="Summarization Model:").grid(row=2, column=0, sticky=tk.W, pady=5)
        summarization_model = tk.StringVar(value=self.config.get("summarization_model", "llama2"))
        ttk.Entry(ollama_frame, textvariable=summarization_model).grid(row=2, column=1, sticky=tk.W, pady=5)
        
        # Audio settings
        ttk.Label(audio_frame, text="Sample Rate:").grid(row=0, column=0, sticky=tk.W, pady=5)
        sample_rate = tk.StringVar(value=str(self.config.get("sample_rate", 16000)))
        ttk.Entry(audio_frame, textvariable=sample_rate).grid(row=0, column=1, sticky=tk.W, pady=5)
        
        ttk.Label(audio_frame, text="Channels:").grid(row=1, column=0, sticky=tk.W, pady=5)
        channels = tk.StringVar(value=str(self.config.get("channels", 1)))
        ttk.Entry(audio_frame, textvariable=channels).grid(row=1, column=1, sticky=tk.W, pady=5)
        
        ttk.Label(audio_frame, text="Chunk Size:").grid(row=2, column=0, sticky=tk.W, pady=5)
        chunk_size = tk.StringVar(value=str(self.config.get("chunk_size", 1024)))
        ttk.Entry(audio_frame, textvariable=chunk_size).grid(row=2, column=1, sticky=tk.W, pady=5)
        
        # Buttons
        def save_settings():
            try:
                # Update config
                self.config.set("summary_interval", int(summary_interval.get()))
                self.config.set("ollama_host", ollama_host.get())
                self.config.set("transcription_model", transcription_model.get())
                self.config.set("summarization_model", summarization_model.get())
                self.config.set("sample_rate", int(sample_rate.get()))
                self.config.set("channels", int(channels.get()))
                self.config.set("chunk_size", int(chunk_size.get()))
                
                # Save config
                self.config.save()
                
                messagebox.showinfo("Success", "Settings saved successfully")
                settings_window.destroy()
            except Exception as e:
                messagebox.showerror("Error", f"Failed to save settings: {str(e)}")
        
        button_frame = ttk.Frame(settings_window)
        button_frame.pack(fill=tk.X, padx=10, pady=10)
        
        ttk.Button(button_frame, text="Save", command=save_settings).pack(side=tk.RIGHT, padx=5)
        ttk.Button(button_frame, text="Cancel", command=settings_window.destroy).pack(side=tk.RIGHT, padx=5)
    
    def show_about(self):
        """Show the about dialog"""
        about_text = """SumOnTheFly v1.0
        
A desktop application that records audio, transcribes it in real-time, and generates bullet-point summaries.

Created with Python and Tkinter.
Uses Ollama for local AI processing.

Ollama Server: http://CarlsMacStudio.got.volvo.net:11434
Transcription Model: dimavz/whisper-tiny
Summarization Model: qwen3
        """
        messagebox.showinfo("About SumOnTheFly", about_text)

def main():
    """Main entry point for the application"""
    try:
        # Create the root window
        root = tk.Tk()
        
        # Create and run the application
        app = SumOnTheFlyApp(root)
        root.mainloop()
        
    except Exception as e:
        logger.critical(f"Unhandled exception: {str(e)}")
        messagebox.showerror("Critical Error", f"An unhandled error occurred: {str(e)}")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
