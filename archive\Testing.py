import os
from dotenv import load_dotenv
from openai import OpenAI
import ell
from pathlib import Path

# Ladda din Ollama-URL
load_dotenv()
client = OpenAI(
    base_url=os.getenv("OLLAMA_URL"),  # t.ex. "http://localhost:11434"
    api_key="ollama",
)

# Definiera ett eget verktyg för transkription
@ell.tool()
def transcribe(file_path: Path) -> str:
    """Transkribera en ljudfil med Whisper-modellen."""
    with open(file_path, "rb") as audio_file:
        transcription = client.audio.transcriptions.create(
            file=audio_file,
            model="dimavz/whisper-tiny:latest",
            response_format="text"
        )
    return transcription

# Använd i en LMP
@ell.simple(model="gemma3:27b", client=client)
def summarize_transcript(file_path: Path) -> str:
    text = transcribe(file_path)
    return f"Ge mig en sammanfattning av följande transcript:\n\n{text}"

# <PERSON>ö<PERSON>
print(summarize_transcript(Path("C:\\python\\SumOnTheFly-1\\test_recording.wav")))
