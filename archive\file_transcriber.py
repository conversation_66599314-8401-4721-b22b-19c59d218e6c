#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import threading
import time
import numpy as np
import logging
import sys
import os
import requests
import json
from datetime import datetime

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("file_transcriber.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class FileTranscriber:
    """Application for transcribing audio files using Ollama"""
    
    def __init__(self, root):
        self.root = root
        self.root.title("SumOnTheFly - File Transcriber")
        self.root.geometry("800x600")
        
        # Ollama settings
        self.ollama_host = "http://CarlsMacStudio.got.volvo.net:11434"
        self.transcription_model = "dimavz/whisper-tiny:latest"
        self.summarization_model = "qwen3"
        
        # Set up UI
        self.setup_ui()
        
        # Test connection
        self.test_connection()
    
    def setup_ui(self):
        """Set up the user interface"""
        # Main frame
        main_frame = ttk.Frame(self.root, padding=10)
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # File selection
        file_frame = ttk.LabelFrame(main_frame, text="Audio File")
        file_frame.pack(fill=tk.X, pady=5)
        
        self.file_var = tk.StringVar()
        file_entry = ttk.Entry(file_frame, textvariable=self.file_var, width=60)
        file_entry.pack(side=tk.LEFT, padx=5, pady=5, fill=tk.X, expand=True)
        
        browse_button = ttk.Button(file_frame, text="Browse", command=self.browse_file)
        browse_button.pack(side=tk.LEFT, padx=5, pady=5)
        
        # Control panel
        control_frame = ttk.Frame(main_frame)
        control_frame.pack(fill=tk.X, pady=5)
        
        self.transcribe_button = ttk.Button(
            control_frame, 
            text="Transcribe", 
            command=self.transcribe_file
        )
        self.transcribe_button.pack(side=tk.LEFT, padx=5, pady=5)
        
        self.summarize_button = ttk.Button(
            control_frame, 
            text="Summarize", 
            command=self.summarize_text,
            state=tk.DISABLED
        )
        self.summarize_button.pack(side=tk.LEFT, padx=5, pady=5)
        
        self.save_button = ttk.Button(
            control_frame, 
            text="Save Transcript", 
            command=self.save_transcript,
            state=tk.DISABLED
        )
        self.save_button.pack(side=tk.LEFT, padx=5, pady=5)
        
        # Transcription panel
        transcription_frame = ttk.LabelFrame(main_frame, text="Transcription")
        transcription_frame.pack(fill=tk.BOTH, expand=True, pady=5)
        
        self.transcription_text = tk.Text(
            transcription_frame,
            wrap=tk.WORD,
            height=10
        )
        transcription_scroll = ttk.Scrollbar(
            transcription_frame, 
            command=self.transcription_text.yview
        )
        self.transcription_text.configure(yscrollcommand=transcription_scroll.set)
        
        self.transcription_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=5, pady=5)
        transcription_scroll.pack(side=tk.RIGHT, fill=tk.Y, pady=5)
        
        # Summary panel
        summary_frame = ttk.LabelFrame(main_frame, text="Summary")
        summary_frame.pack(fill=tk.BOTH, expand=True, pady=5)
        
        self.summary_text = tk.Text(
            summary_frame,
            wrap=tk.WORD,
            height=5
        )
        summary_scroll = ttk.Scrollbar(
            summary_frame, 
            command=self.summary_text.yview
        )
        self.summary_text.configure(yscrollcommand=summary_scroll.set)
        
        self.summary_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=5, pady=5)
        summary_scroll.pack(side=tk.RIGHT, fill=tk.Y, pady=5)
        
        # Status bar
        self.status_bar = ttk.Label(
            self.root, 
            text="Ready", 
            relief=tk.SUNKEN, 
            anchor=tk.W
        )
        self.status_bar.pack(side=tk.BOTTOM, fill=tk.X)
    
    def test_connection(self):
        """Test connection to Ollama server"""
        try:
            response = requests.get(f"{self.ollama_host}/api/version")
            if response.status_code == 200:
                version = response.json().get("version", "unknown")
                self.status_bar.config(text=f"Connected to Ollama server (version {version})")
                return True
            else:
                self.status_bar.config(text=f"Failed to connect to Ollama server: {response.status_code}")
                return False
        except Exception as e:
            self.status_bar.config(text=f"Error connecting to Ollama server: {str(e)}")
            return False
    
    def browse_file(self):
        """Browse for an audio file"""
        file_path = filedialog.askopenfilename(
            title="Select Audio File",
            filetypes=[("WAV files", "*.wav"), ("MP3 files", "*.mp3"), ("All files", "*.*")]
        )
        
        if file_path:
            self.file_var.set(file_path)
    
    def transcribe_file(self):
        """Transcribe the selected audio file"""
        file_path = self.file_var.get()
        
        if not file_path or not os.path.exists(file_path):
            messagebox.showerror("Error", "Please select a valid audio file.")
            return
        
        # Clear previous transcription
        self.transcription_text.delete(1.0, tk.END)
        self.summary_text.delete(1.0, tk.END)
        self.summarize_button.config(state=tk.DISABLED)
        self.save_button.config(state=tk.DISABLED)
        
        # Update status
        self.status_bar.config(text=f"Transcribing {os.path.basename(file_path)}...")
        
        # Start transcription in a separate thread
        threading.Thread(
            target=self._transcribe_file,
            args=(file_path,),
            daemon=True
        ).start()
    
    def _transcribe_file(self, file_path):
        """Transcribe audio file using Ollama's generate API"""
        try:
            # Since we found that the /api/audio endpoint doesn't exist,
            # we'll use the generate API instead with a prompt that asks
            # for transcription
            
            # First, check if it's a valid audio file
            if not os.path.exists(file_path):
                self.root.after(0, self._show_error, "File not found")
                return
            
            # Get file info
            file_size = os.path.getsize(file_path) / (1024 * 1024)  # Size in MB
            file_name = os.path.basename(file_path)
            
            # Check if file is too large
            if file_size > 25:  # Limit to 25MB
                self.root.after(0, self._show_error, f"File is too large ({file_size:.1f} MB). Maximum size is 25MB.")
                return
            
            # Create a prompt for the LLM
            prompt = f"""I have an audio file named '{file_name}'. Please transcribe it for me. 
            The audio file contains spoken content that needs to be converted to text.
            Just provide the transcription without any additional commentary."""
            
            # Send to Ollama for transcription using the generate API
            url = f"{self.ollama_host}/api/generate"
            data = {
                "model": self.transcription_model,
                "prompt": prompt,
                "stream": False
            }
            
            logger.info(f"Sending request to {url} with model {self.transcription_model}")
            self.status_bar.config(text=f"Sending transcription request to Ollama with model {self.transcription_model}...")
            
            response = requests.post(url, json=data)
            
            logger.info(f"Response status: {response.status_code}")
            
            if response.status_code == 200:
                result = response.json()
                text = result.get('response', '')
                logger.info(f"Transcription result received: {len(text)} characters")
                
                # Update UI on the main thread
                self.root.after(0, self._update_transcription_ui, text)
            else:
                error_msg = f"Transcription failed: {response.status_code} - {response.text}"
                logger.error(error_msg)
                self.root.after(0, self._show_error, error_msg)
                
        except Exception as e:
            error_msg = f"Error in transcription: {str(e)}"
            logger.error(error_msg)
            self.root.after(0, self._show_error, error_msg)
    
    def _update_transcription_ui(self, text):
        """Update the transcription UI"""
        self.transcription_text.delete(1.0, tk.END)
        self.transcription_text.insert(tk.END, text)
        self.status_bar.config(text="Transcription complete")
        self.summarize_button.config(state=tk.NORMAL)
        self.save_button.config(state=tk.NORMAL)
    
    def summarize_text(self):
        """Summarize the transcribed text"""
        text = self.transcription_text.get(1.0, tk.END).strip()
        
        if not text:
            messagebox.showinfo("Info", "No text to summarize")
            return
        
        # Clear previous summary
        self.summary_text.delete(1.0, tk.END)
        
        # Update status
        self.status_bar.config(text="Generating summary...")
        
        # Start summarization in a separate thread
        threading.Thread(
            target=self._summarize_text,
            args=(text,),
            daemon=True
        ).start()
    
    def _summarize_text(self, text):
        """Summarize text using Ollama"""
        try:
            # Create a prompt for summarization
            prompt = f"""Summarize the following text in 3-5 bullet points, highlighting the key points:

{text}"""
            
            # Send to Ollama for summarization
            url = f"{self.ollama_host}/api/generate"
            data = {
                "model": self.summarization_model,
                "prompt": prompt,
                "stream": False
            }
            
            logger.info(f"Sending summarization request with model {self.summarization_model}")
            response = requests.post(url, json=data)
            
            if response.status_code == 200:
                result = response.json()
                summary = result.get('response', '')
                logger.info(f"Summary received: {len(summary)} characters")
                
                # Update UI on the main thread
                self.root.after(0, self._update_summary_ui, summary)
            else:
                error_msg = f"Summarization failed: {response.status_code} - {response.text}"
                logger.error(error_msg)
                self.root.after(0, self._show_error, error_msg)
                
        except Exception as e:
            error_msg = f"Error in summarization: {str(e)}"
            logger.error(error_msg)
            self.root.after(0, self._show_error, error_msg)
    
    def _update_summary_ui(self, text):
        """Update the summary UI"""
        self.summary_text.delete(1.0, tk.END)
        self.summary_text.insert(tk.END, text)
        self.status_bar.config(text="Summary complete")
    
    def save_transcript(self):
        """Save the transcription to a text file"""
        text = self.transcription_text.get(1.0, tk.END).strip()
        
        if not text:
            messagebox.showinfo("Info", "No text to save")
            return
        
        try:
            # Ask for save location
            filename = filedialog.asksaveasfilename(
                defaultextension=".txt",
                filetypes=[("Text files", "*.txt"), ("All files", "*.*")]
            )
            
            if not filename:
                return
            
            # Save as text file
            with open(filename, 'w') as f:
                f.write(text)
            
            self.status_bar.config(text=f"Transcript saved to {os.path.basename(filename)}")
            logger.info(f"Saved transcript to {filename}")
            
            messagebox.showinfo("Success", f"Transcript saved to {filename}")
            
        except Exception as e:
            logger.error(f"Error saving transcript: {str(e)}")
            messagebox.showerror("Error", f"Failed to save transcript: {str(e)}")
    
    def _show_error(self, message):
        """Show error message"""
        self.status_bar.config(text=message)
        messagebox.showerror("Error", message)

def main():
    """Main entry point for the application"""
    try:
        # Create the root window
        root = tk.Tk()
        
        # Create and run the application
        app = FileTranscriber(root)
        
        root.mainloop()
        
    except Exception as e:
        logger.critical(f"Unhandled exception: {str(e)}")
        messagebox.showerror("Critical Error", f"An unhandled error occurred: {str(e)}")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
