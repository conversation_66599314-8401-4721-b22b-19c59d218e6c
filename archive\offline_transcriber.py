#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import threading
import time
import numpy as np
import logging
import sys
import os
from datetime import datetime
import random

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("offline_transcriber.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# Check for PyAudio
try:
    import pyaudio
    import wave
    AUDIO_AVAILABLE = True
    logger.info("PyAudio and wave modules are available")
except ImportError as e:
    AUDIO_AVAILABLE = False
    logger.error(f"Audio modules not available: {str(e)}")

class OfflineTranscriber:
    """Offline transcription application with simulated AI responses"""
    
    def __init__(self, root):
        self.root = root
        self.root.title("SumOnTheFly - Offline Mode")
        self.root.geometry("800x600")
        
        # Audio parameters
        self.sample_rate = 16000
        self.channels = 1
        self.chunk_size = 1024
        self.format = pyaudio.paInt16 if AUDIO_AVAILABLE else None
        
        # State variables
        self.is_recording = False
        self.audio_data = []
        self.selected_device_index = None
        self.p = None
        self.stream = None
        self.recording_thread = None
        self.recording_start_time = 0
        self.audio_level = 0
        
        # Set up UI
        self.setup_ui()
        
        # Detect audio devices
        self.detect_audio_devices()
        
        # Start UI update timer
        self.update_ui()
    
    def setup_ui(self):
        """Set up the user interface"""
        # Main frame
        main_frame = ttk.Frame(self.root, padding=10)
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # Mode indicator
        mode_frame = ttk.Frame(main_frame)
        mode_frame.pack(fill=tk.X, pady=5)
        
        mode_label = ttk.Label(
            mode_frame,
            text="OFFLINE MODE - Using Simulated AI Responses",
            font=("Arial", 10, "bold"),
            foreground="red"
        )
        mode_label.pack(pady=5)
        
        # Device selection
        device_frame = ttk.LabelFrame(main_frame, text="Audio Device")
        device_frame.pack(fill=tk.X, pady=5)
        
        self.device_var = tk.StringVar()
        self.device_dropdown = ttk.Combobox(device_frame, textvariable=self.device_var, state="readonly", width=40)
        self.device_dropdown.pack(side=tk.LEFT, padx=5, pady=5, fill=tk.X, expand=True)
        
        refresh_button = ttk.Button(device_frame, text="Refresh", command=self.detect_audio_devices)
        refresh_button.pack(side=tk.RIGHT, padx=5, pady=5)
        
        # Control panel
        control_frame = ttk.LabelFrame(main_frame, text="Controls")
        control_frame.pack(fill=tk.X, pady=5)
        
        self.record_button = ttk.Button(
            control_frame, 
            text="Start Recording", 
            command=self.toggle_recording
        )
        self.record_button.pack(side=tk.LEFT, padx=5, pady=5)
        
        self.transcribe_button = ttk.Button(
            control_frame, 
            text="Transcribe", 
            command=self.transcribe_recording,
            state=tk.DISABLED
        )
        self.transcribe_button.pack(side=tk.LEFT, padx=5, pady=5)
        
        self.summarize_button = ttk.Button(
            control_frame, 
            text="Summarize", 
            command=self.summarize_text,
            state=tk.DISABLED
        )
        self.summarize_button.pack(side=tk.LEFT, padx=5, pady=5)
        
        self.save_button = ttk.Button(
            control_frame, 
            text="Save Recording", 
            command=self.save_recording,
            state=tk.DISABLED
        )
        self.save_button.pack(side=tk.LEFT, padx=5, pady=5)
        
        self.time_label = ttk.Label(control_frame, text="00:00:00")
        self.time_label.pack(side=tk.RIGHT, padx=5, pady=5)
        
        # Audio level visualization
        level_frame = ttk.LabelFrame(main_frame, text="Audio Level")
        level_frame.pack(fill=tk.X, pady=5)
        
        self.level_canvas = tk.Canvas(level_frame, height=30, bg="white")
        self.level_canvas.pack(fill=tk.X, padx=5, pady=5)
        
        self.level_label = ttk.Label(level_frame, text="Level: 0.0%")
        self.level_label.pack(pady=2)
        
        # Transcription panel
        transcription_frame = ttk.LabelFrame(main_frame, text="Transcription")
        transcription_frame.pack(fill=tk.BOTH, expand=True, pady=5)
        
        self.transcription_text = tk.Text(
            transcription_frame,
            wrap=tk.WORD,
            height=10
        )
        transcription_scroll = ttk.Scrollbar(
            transcription_frame, 
            command=self.transcription_text.yview
        )
        self.transcription_text.configure(yscrollcommand=transcription_scroll.set)
        
        self.transcription_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=5, pady=5)
        transcription_scroll.pack(side=tk.RIGHT, fill=tk.Y, pady=5)
        
        # Summary panel
        summary_frame = ttk.LabelFrame(main_frame, text="Summary")
        summary_frame.pack(fill=tk.BOTH, expand=True, pady=5)
        
        self.summary_text = tk.Text(
            summary_frame,
            wrap=tk.WORD,
            height=5
        )
        summary_scroll = ttk.Scrollbar(
            summary_frame, 
            command=self.summary_text.yview
        )
        self.summary_text.configure(yscrollcommand=summary_scroll.set)
        
        self.summary_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=5, pady=5)
        summary_scroll.pack(side=tk.RIGHT, fill=tk.Y, pady=5)
        
        # Status bar
        self.status_bar = ttk.Label(
            self.root, 
            text="Ready (Offline Mode)", 
            relief=tk.SUNKEN, 
            anchor=tk.W
        )
        self.status_bar.pack(side=tk.BOTTOM, fill=tk.X)
    
    def detect_audio_devices(self):
        """Detect available audio devices"""
        if not AUDIO_AVAILABLE:
            messagebox.showerror("Error", "PyAudio is not available. Please install it to use audio recording.")
            return
        
        try:
            p = pyaudio.PyAudio()
            device_count = p.get_device_count()
            devices = []
            device_indices = []
            
            # Get all input devices
            for i in range(device_count):
                try:
                    device_info = p.get_device_info_by_index(i)
                    if device_info.get('maxInputChannels', 0) > 0:
                        devices.append(f"{device_info.get('name')}")
                        device_indices.append(i)
                        logger.info(f"Found input device: {device_info.get('name')} (index: {i})")
                except Exception as e:
                    logger.warning(f"Error getting info for device {i}: {str(e)}")
            
            # Update dropdown
            self.device_dropdown['values'] = devices
            if devices:
                self.device_dropdown.current(0)
                self.selected_device_index = device_indices[0]
                self.status_bar.config(text=f"Found {len(devices)} audio devices (Offline Mode)")
            else:
                self.status_bar.config(text="No audio input devices found (Offline Mode)")
            
            # Clean up
            p.terminate()
            
        except Exception as e:
            logger.error(f"Error detecting audio devices: {str(e)}")
            messagebox.showerror("Error", f"Error detecting audio devices: {str(e)}")
    
    def update_ui(self):
        """Periodically update the UI"""
        if self.is_recording:
            # Update recording time
            elapsed = time.time() - self.recording_start_time
            hours, remainder = divmod(int(elapsed), 3600)
            minutes, seconds = divmod(remainder, 60)
            self.time_label.config(text=f"{hours:02d}:{minutes:02d}:{seconds:02d}")
            
            # Update audio level visualization
            self.update_level_visualization()
        
        # Schedule next update
        self.root.after(100, self.update_ui)
    
    def update_level_visualization(self):
        """Update the audio level visualization"""
        # Clear canvas
        self.level_canvas.delete("all")
        
        # Get canvas dimensions
        width = self.level_canvas.winfo_width()
        height = self.level_canvas.winfo_height()
        
        # Avoid division by zero
        if width < 10:
            width = 200  # Default width
        
        # Draw background
        self.level_canvas.create_rectangle(0, 0, width, height, fill="white")
        
        # Draw level bar
        level_width = int(width * min(1.0, self.audio_level / 100.0))
        
        # Color based on level
        if self.audio_level < 30:
            color = "green"
        elif self.audio_level < 70:
            color = "yellow"
        else:
            color = "red"
        
        self.level_canvas.create_rectangle(0, 0, level_width, height, fill=color)
        
        # Update level label
        self.level_label.config(text=f"Level: {self.audio_level:.1f}%")
    
    def toggle_recording(self):
        """Start or stop recording"""
        if not self.is_recording:
            self.start_recording()
        else:
            self.stop_recording()
    
    def start_recording(self):
        """Start recording audio"""
        if not AUDIO_AVAILABLE:
            messagebox.showerror("Error", "PyAudio is not available. Please install it to use audio recording.")
            return
        
        try:
            # Get selected device
            selected_index = self.device_dropdown.current()
            if selected_index < 0:
                messagebox.showerror("Error", "Please select an audio device")
                return
            
            # Clear previous recording
            self.audio_data = []
            self.audio_level = 0
            self.transcription_text.delete(1.0, tk.END)
            self.summary_text.delete(1.0, tk.END)
            
            # Initialize PyAudio
            self.p = pyaudio.PyAudio()
            
            # Open stream
            self.stream = self.p.open(
                format=self.format,
                channels=self.channels,
                rate=self.sample_rate,
                input=True,
                input_device_index=self.selected_device_index,
                frames_per_buffer=self.chunk_size
            )
            
            # Update state and UI
            self.is_recording = True
            self.recording_start_time = time.time()
            self.record_button.config(text="Stop Recording")
            self.transcribe_button.config(state=tk.DISABLED)
            self.summarize_button.config(state=tk.DISABLED)
            self.save_button.config(state=tk.DISABLED)
            self.status_bar.config(text="Recording... (Offline Mode)")
            
            # Start recording thread
            self.recording_thread = threading.Thread(target=self._record_audio)
            self.recording_thread.daemon = True
            self.recording_thread.start()
            
            logger.info("Started recording")
            
        except Exception as e:
            logger.error(f"Error starting recording: {str(e)}")
            messagebox.showerror("Error", f"Failed to start recording: {str(e)}")
            self.cleanup()
    
    def _record_audio(self):
        """Record audio in a separate thread"""
        try:
            while self.is_recording and self.stream:
                try:
                    # Read audio data
                    data = self.stream.read(self.chunk_size, exception_on_overflow=False)
                    self.audio_data.append(data)
                    
                    # Calculate audio level
                    audio_array = np.frombuffer(data, dtype=np.int16)
                    max_value = np.max(np.abs(audio_array))
                    # Convert to percentage (0-100)
                    self.audio_level = min(100, (max_value / 32767) * 100)
                    
                except Exception as e:
                    logger.error(f"Error reading audio: {str(e)}")
                    time.sleep(0.1)  # Avoid tight loop on errors
        except Exception as e:
            logger.error(f"Error in recording thread: {str(e)}")
        finally:
            logger.info("Recording thread finished")
    
    def stop_recording(self):
        """Stop recording audio"""
        if not self.is_recording:
            return
        
        try:
            # Signal recording thread to stop
            self.is_recording = False
            
            # Wait for recording thread to finish (with timeout)
            if self.recording_thread and self.recording_thread.is_alive():
                self.recording_thread.join(timeout=1.0)
            
            # Close stream
            if self.stream:
                try:
                    self.stream.stop_stream()
                    self.stream.close()
                except Exception as e:
                    logger.error(f"Error closing stream: {e}")
                finally:
                    self.stream = None
            
            # Terminate PyAudio
            if self.p:
                try:
                    self.p.terminate()
                except Exception as e:
                    logger.error(f"Error terminating PyAudio: {e}")
                finally:
                    self.p = None
            
            # Update UI
            self.record_button.config(text="Start Recording")
            self.transcribe_button.config(state=tk.NORMAL if self.audio_data else tk.DISABLED)
            self.save_button.config(state=tk.NORMAL if self.audio_data else tk.DISABLED)
            self.status_bar.config(text="Recording stopped (Offline Mode)")
            
            logger.info("Stopped recording")
            
        except Exception as e:
            logger.error(f"Error stopping recording: {str(e)}")
            messagebox.showerror("Error", f"Error stopping recording: {str(e)}")
    
    def transcribe_recording(self):
        """Simulate transcription of the recorded audio"""
        if not self.audio_data:
            messagebox.showinfo("Info", "No audio data to transcribe")
            return
        
        try:
            # Clear previous transcription and summary
            self.transcription_text.delete(1.0, tk.END)
            self.summary_text.delete(1.0, tk.END)
            
            # Update status
            self.status_bar.config(text="Simulating transcription... (Offline Mode)")
            
            # Start simulated transcription in a separate thread
            threading.Thread(
                target=self._simulate_transcription,
                daemon=True
            ).start()
            
        except Exception as e:
            logger.error(f"Error in transcription: {str(e)}")
            messagebox.showerror("Error", f"Error in transcription: {str(e)}")
    
    def _simulate_transcription(self):
        """Simulate a transcription process"""
        try:
            # Calculate recording duration in seconds
            duration = len(self.audio_data) * self.chunk_size / self.sample_rate
            
            # Generate simulated transcription based on recording duration
            transcription = self._generate_simulated_transcription(duration)
            
            # Simulate processing delay
            for i in range(5):
                self.status_bar.config(text=f"Simulating transcription... {i+1}/5 (Offline Mode)")
                time.sleep(0.5)
            
            # Update UI on the main thread
            self.root.after(0, self._update_transcription_ui, transcription)
            
        except Exception as e:
            logger.error(f"Error in simulated transcription: {str(e)}")
            self.root.after(0, self._show_error, f"Error in simulated transcription: {str(e)}")
    
    def _generate_simulated_transcription(self, duration):
        """Generate a simulated transcription based on recording duration"""
        # Sample sentences for simulated transcription
        sentences = [
            "Welcome to the SumOnTheFly application.",
            "This is a demonstration of the offline mode.",
            "The application is currently using simulated transcription.",
            "In this mode, we generate placeholder text instead of using Ollama.",
            "This allows you to test the application without an active connection.",
            "You can record audio and see how the interface works.",
            "The audio level meter shows your microphone input in real-time.",
            "After recording, you can transcribe and summarize your audio.",
            "The transcription is simulated based on the recording duration.",
            "Similarly, the summary is generated from the simulated transcription.",
            "This offline mode is useful when you don't have access to the Ollama server.",
            "It provides a way to demonstrate the application's functionality.",
            "You can also save your recordings for later use.",
            "The application supports various audio formats.",
            "Thank you for using SumOnTheFly in offline mode."
        ]
        
        # Determine how many sentences to include based on duration
        num_sentences = max(3, min(len(sentences), int(duration / 2)))
        
        # Randomly select sentences
        selected_sentences = random.sample(sentences, num_sentences)
        
        # Add some repetition for longer recordings
        if duration > 20:
            additional_sentences = random.sample(sentences, min(len(sentences), int(duration / 5)))
            selected_sentences.extend(additional_sentences)
        
        # Shuffle the sentences to make it seem more natural
        random.shuffle(selected_sentences)
        
        return " ".join(selected_sentences)
    
    def _update_transcription_ui(self, text):
        """Update the transcription UI"""
        self.transcription_text.delete(1.0, tk.END)
        self.transcription_text.insert(tk.END, text)
        self.status_bar.config(text="Transcription complete (Offline Mode)")
        self.summarize_button.config(state=tk.NORMAL)
    
    def summarize_text(self):
        """Simulate summarization of the transcribed text"""
        text = self.transcription_text.get(1.0, tk.END).strip()
        
        if not text:
            messagebox.showinfo("Info", "No text to summarize")
            return
        
        try:
            # Clear previous summary
            self.summary_text.delete(1.0, tk.END)
            
            # Update status
            self.status_bar.config(text="Simulating summarization... (Offline Mode)")
            
            # Start simulated summarization in a separate thread
            threading.Thread(
                target=self._simulate_summarization,
                args=(text,),
                daemon=True
            ).start()
            
        except Exception as e:
            logger.error(f"Error in summarization: {str(e)}")
            messagebox.showerror("Error", f"Error in summarization: {str(e)}")
    
    def _simulate_summarization(self, text):
        """Simulate a summarization process"""
        try:
            # Simulate processing delay
            for i in range(3):
                self.status_bar.config(text=f"Simulating summarization... {i+1}/3 (Offline Mode)")
                time.sleep(0.7)
            
            # Generate simulated summary
            summary = self._generate_simulated_summary(text)
            
            # Update UI on the main thread
            self.root.after(0, self._update_summary_ui, summary)
            
        except Exception as e:
            logger.error(f"Error in simulated summarization: {str(e)}")
            self.root.after(0, self._show_error, f"Error in simulated summarization: {str(e)}")
    
    def _generate_simulated_summary(self, text):
        """Generate a simulated summary"""
        # Sample bullet points for simulated summary
        bullet_points = [
            "• The audio contains information about the SumOnTheFly application.",
            "• The speaker discusses the offline mode functionality.",
            "• The recording demonstrates how the application works without an active server connection.",
            "• Various features of the application are mentioned, including audio recording and transcription.",
            "• The speaker explains how simulated responses are generated based on recording duration."
        ]
        
        # Randomly select 3-5 bullet points
        num_points = random.randint(3, 5)
        selected_points = random.sample(bullet_points, num_points)
        
        return "\n".join(selected_points)
    
    def _update_summary_ui(self, text):
        """Update the summary UI"""
        self.summary_text.delete(1.0, tk.END)
        self.summary_text.insert(tk.END, text)
        self.status_bar.config(text="Summary complete (Offline Mode)")
    
    def save_recording(self):
        """Save the recorded audio to a file"""
        if not self.audio_data:
            messagebox.showinfo("Info", "No audio data to save")
            return
        
        try:
            # Ask for save location
            filename = filedialog.asksaveasfilename(
                defaultextension=".wav",
                filetypes=[("WAV files", "*.wav"), ("All files", "*.*")]
            )
            
            if not filename:
                return
            
            # Save as WAV file
            with wave.open(filename, 'wb') as wf:
                wf.setnchannels(self.channels)
                wf.setsampwidth(2)  # 2 bytes for paInt16
                wf.setframerate(self.sample_rate)
                wf.writeframes(b''.join(self.audio_data))
            
            self.status_bar.config(text=f"Saved to {os.path.basename(filename)} (Offline Mode)")
            logger.info(f"Saved recording to {filename}")
            
            messagebox.showinfo("Success", f"Recording saved to {filename}")
            
        except Exception as e:
            logger.error(f"Error saving recording: {str(e)}")
            messagebox.showerror("Error", f"Failed to save recording: {str(e)}")
    
    def _show_error(self, message):
        """Show error message"""
        self.status_bar.config(text=f"{message} (Offline Mode)")
        messagebox.showerror("Error", message)

def main():
    """Main entry point for the application"""
    try:
        # Create the root window
        root = tk.Tk()
        
        # Create and run the application
        app = OfflineTranscriber(root)
        
        # Handle window close
        def on_closing():
            if app.is_recording:
                if messagebox.askyesno("Confirm", "Recording is in progress. Stop recording and exit?"):
                    app.stop_recording()
                    root.destroy()
            else:
                root.destroy()
        
        root.protocol("WM_DELETE_WINDOW", on_closing)
        root.mainloop()
        
    except Exception as e:
        logger.critical(f"Unhandled exception: {str(e)}")
        messagebox.showerror("Critical Error", f"An unhandled error occurred: {str(e)}")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
