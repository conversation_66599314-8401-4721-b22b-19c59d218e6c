import requests
import logging
import json
import threading
import time
import random
from typing import Optional, Callable, Dict, Any

logger = logging.getLogger(__name__)

class SummarizationService:
    """
    Service for generating bullet-point summaries of transcribed text using Ollama LLMs.
    """
    
    def __init__(self, 
                 ollama_host: str = "http://localhost:11434",
                 model: str = "llama2",
                 on_summary: Optional[Callable[[str], None]] = None):
        self.ollama_host = ollama_host
        self.model = model
        self.on_summary = on_summary
        self.is_processing = False
        self.model_params = {
            "temperature": 0.7,
            "top_p": 0.9,
            "max_tokens": 500
        }
        self.test_mode = False
        
        # Test connection to Ollama server
        try:
            self._test_ollama_connection()
        except Exception as e:
            logger.warning(f"Could not connect to Ollama server at {ollama_host}: {str(e)}")
            logger.warning("Summarization service will run in test mode with simulated responses")
            self.test_mode = True
    
    def _test_ollama_connection(self):
        """Test connection to Ollama server"""
        try:
            response = requests.get(f"{self.ollama_host}/api/version", timeout=5)
            if response.status_code == 200:
                version = response.json().get("version", "unknown")
                logger.info(f"Connected to Ollama server (version {version})")
            else:
                raise Exception(f"Failed to connect: {response.status_code}")
        except Exception as e:
            logger.error(f"Error connecting to Ollama: {str(e)}")
            raise
    
    def summarize_text(self, text: str, prompt_template: str = "Summarize the key points in 3-5 bullets.") -> None:
        """Summarize the provided text asynchronously"""
        if not text.strip():
            logger.warning("Empty text provided for summarization")
            return
        
        # Start a new thread to avoid blocking
        thread = threading.Thread(
            target=self._generate_summary,
            args=(text, prompt_template),
            daemon=True
        )
        thread.start()
    
    def _generate_summary(self, text: str, prompt_template: str) -> None:
        """Generate a summary using Ollama LLM"""
        try:
            self.is_processing = True
            
            # Construct the full prompt
            full_prompt = f"{prompt_template}\n\nText to summarize:\n{text}"
            
            if self.test_mode:
                # Generate a simulated summary in test mode
                summary = self._simulate_summary(text)
            else:
                # Call Ollama API
                summary = self._call_ollama_api(full_prompt)
            
            # Call the callback with the summary
            if summary and self.on_summary:
                self.on_summary(summary)
                
        except Exception as e:
            logger.error(f"Error generating summary: {str(e)}")
        finally:
            self.is_processing = False
    
    def _call_ollama_api(self, prompt: str) -> str:
        """Call Ollama API to generate text"""
        try:
            url = f"{self.ollama_host}/api/generate"
            
            # Prepare the request payload
            payload = {
                "model": self.model,
                "prompt": prompt,
                "stream": False,
                "options": self.model_params
            }
            
            # Send request to Ollama with timeout
            response = requests.post(url, json=payload, timeout=30)
            
            if response.status_code == 200:
                result = response.json()
                return result.get('response', '')
            else:
                logger.error(f"Summarization API error: {response.status_code} - {response.text}")
                return ""
                
        except Exception as e:
            logger.error(f"Error in summarization request: {str(e)}")
            return ""
    
    def _simulate_summary(self, text: str) -> str:
        """Generate a simulated summary for testing"""
        # Extract some words from the text to make the summary seem related
        words = text.split()
        sample_words = random.sample(words, min(5, len(words))) if words else ["sample", "test", "data"]
        
        # Generate different test bullet points
        test_summaries = [
            f"- This is a simulated summary in test mode\n- Contains words like {' '.join(sample_words[:2])}\n- No connection to Ollama server",
            f"- Application running without Ollama connection\n- Test summary with {len(words)} words of input\n- Sample content: {' '.join(sample_words[:3])}",
            f"- Simulated bullet point summary\n- Contains {len(text)} characters of text\n- Example keywords: {' '.join(sample_words[:2])}"
        ]
        
        # Select a summary based on text length
        summary_index = hash(text) % len(test_summaries)
        
        # Sleep to simulate processing time
        time.sleep(1.0)  # Simulate LLM processing time
        
        return test_summaries[summary_index]
    
    def set_model(self, model: str) -> None:
        """Set the LLM model to use"""
        self.model = model
    
    def set_model_params(self, params: Dict[str, Any]) -> None:
        """Set model parameters like temperature, top_p, etc."""
        self.model_params = params


# Example usage
if __name__ == "__main__":
    logging.basicConfig(level=logging.INFO)
    
    def on_summary_result(summary):
        print(f"Summary:\n{summary}")
    
    # Create summarization service
    service = SummarizationService(
        on_summary=on_summary_result
    )
    
    # Example text to summarize
    example_text = """
    In today's meeting, we discussed the quarterly financial results. 
    Revenue increased by 15% compared to the previous quarter, primarily due to the launch of our new product line. 
    Operating expenses were higher than expected, mainly because of increased marketing costs. 
    The team agreed to focus on cost optimization in the next quarter while maintaining the current growth trajectory. 
    We also discussed the upcoming product roadmap and decided to prioritize the mobile app development.
    The customer satisfaction survey results were positive, with an NPS score of 42, which is an improvement from last quarter's 38.
    """
    
    # Generate summary
    service.summarize_text(example_text)
    
    # Wait for processing to complete
    time.sleep(5)
