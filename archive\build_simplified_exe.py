#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import sys
import shutil
import subprocess
import time
from pathlib import Path

def build_executable():
    """Build the simplified SumOnTheFly executable"""
    print("Building simplified SumOnTheFly executable...")
    
    # Clean any previous build directories
    for dir_name in ['build', 'dist']:
        if os.path.exists(dir_name):
            print(f"Cleaning {dir_name} directory...")
            try:
                shutil.rmtree(dir_name)
            except PermissionError as e:
                print(f"Warning: Could not remove {dir_name} directory: {e}")
                print(f"The executable might be in use. Please close it and try again.")
                # Try to rename the dist directory instead
                if dir_name == 'dist':
                    try:
                        backup_dir = f"{dir_name}_old_{int(time.time())}"
                        print(f"Renaming {dir_name} to {backup_dir}")
                        os.rename(dir_name, backup_dir)
                    except Exception as rename_err:
                        print(f"Could not rename directory: {rename_err}")
                        print("Will attempt to build anyway...")
    
    # Copy config file to ensure it's included
    try:
        # Create a config file with the correct Ollama settings
        config = {
            "ollama_host": "http://CarlsMacStudio.got.volvo.net:11434",
            "transcription_model": "dimavz/whisper-tiny",
            "summarization_model": "qwen3",
            "temp_dir": os.path.join(os.path.expanduser("~"), "SumOnTheFly", "temp")
        }
        
        import json
        with open("config.json", 'w') as f:
            json.dump(config, f, indent=4)
        print("Created config.json with correct Ollama settings")
    except Exception as e:
        print(f"Warning: Could not create config file: {e}")
    
    # PyInstaller command with all necessary options
    cmd = [
        'pyinstaller',
        '--name=SumOnTheFly_Simplified',
        '--onefile',  # Create a single executable file
        '--windowed',  # Don't show console window on Windows
        '--add-data=config.json;.',  # Include config file
        
        # Add hidden imports for core functionality
        '--hidden-import=tkinter',
        '--hidden-import=tkinter.filedialog',
        '--hidden-import=tkinter.messagebox',
        '--hidden-import=tkinter.ttk',
        '--hidden-import=threading',
        '--hidden-import=time',
        '--hidden-import=datetime',
        '--hidden-import=json',
        '--hidden-import=logging',
        '--hidden-import=requests',
        
        # Main script - use the simplified app
        'simplified_app.py'
    ]
    
    # Execute PyInstaller
    print("Running PyInstaller...")
    print(f"Command: {' '.join(cmd)}")
    result = subprocess.run(cmd, capture_output=False)  # Show output in real-time
    
    if result.returncode != 0:
        print("Error building executable")
        return False
    
    print("PyInstaller completed successfully.")
    
    # Check if executable was created
    exe_path = os.path.join('dist', 'SumOnTheFly_Simplified.exe')
    if os.path.exists(exe_path):
        print(f"Executable created at: {os.path.abspath(exe_path)}")
        return True
    else:
        print("Executable was not created. Check the PyInstaller output.")
        return False

if __name__ == "__main__":
    success = build_executable()
    sys.exit(0 if success else 1)
