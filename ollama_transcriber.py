#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import threading
import queue
import sounddevice as sd
import numpy as np
import tkinter as tk
from tkinter import scrolledtext
import tempfile
import wave
import os
import requests
import json
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Ollama server URL
OLLAMA_URL = os.getenv("OLLAMA_URL")

# Check if Ollama server is available
def is_ollama_available():
    try:
        response = requests.get(f"{OLLAMA_URL}/api/tags")
        return response.status_code == 200
    except Exception:
        return False

class TranscriberApp:
    def __init__(self, root):
        self.root = root
        root.title("Live Ollama-transkribering")

        # Textfält för transkript
        self.text = scrolledtext.ScrolledText(root, wrap=tk.WORD, width=60, height=20)
        self.text.pack(padx=10, pady=10)

        # Start/Stop-knappar
        frame = tk.Frame(root)
        frame.pack(pady=5)
        self.btn_start = tk.Button(frame, text="Starta inspelning", command=self.start_recording)
        self.btn_start.pack(side=tk.LEFT, padx=5)
        self.btn_stop = tk.Button(frame, text="Stoppa inspelning", command=self.stop_recording, state=tk.DISABLED)
        self.btn_stop.pack(side=tk.LEFT, padx=5)

        # Queue för ljuddata
        self.q = queue.Queue()
        self.recording = False
        
        # Whisper model name
        self.model_name = "dimavz/whisper-tiny:latest"

        # Check Ollama connection
        if not is_ollama_available():
            self.text.insert(tk.END, f"⚠️ VARNING: Kunde inte ansluta till Ollama-server på {OLLAMA_URL}\n")
            self.text.insert(tk.END, "Kontrollera att servern är igång och tillgänglig.\n")
            self.text.see(tk.END)
            self.btn_start.config(state=tk.DISABLED)
        else:
            self.text.insert(tk.END, f"✅ Ansluten till Ollama-server på {OLLAMA_URL}\n")
            self.text.insert(tk.END, f"Använder modell: {self.model_name}\n")
            self.text.see(tk.END)

    def audio_callback(self, indata, frames, time, status):
        """Körs i ljud‐callback, lägger rådata i q."""
        if status:
            print(status, flush=True)
        self.q.put(indata.copy())

    def start_recording(self):
        self.recording = True
        self.btn_start.config(state=tk.DISABLED)
        self.btn_stop.config(state=tk.NORMAL)
        self.text.insert(tk.END, "🌐 Inspelning pågår...\n")
        self.text.see(tk.END)
        # Starta stream i bakgrundstråd
        threading.Thread(target=self._record, daemon=True).start()

    def _record(self):
        # 16 kHz mono
        with sd.InputStream(samplerate=16000, channels=1, callback=self.audio_callback):
            while self.recording:
                sd.sleep(100)

    def stop_recording(self):
        self.recording = False
        self.btn_start.config(state=tk.NORMAL)
        self.btn_stop.config(state=tk.DISABLED)
        self.text.insert(tk.END, "⏹️ Inspelning stoppad. Transkriberar...\n")
        self.text.see(tk.END)
        # Extrahera allt ljud från kön och skriv till WAV
        threading.Thread(target=self.transcribe_buffer, daemon=True).start()

    def transcribe_buffer(self):
        # Samla ihop all data
        frames = []
        while not self.q.empty():
            frames.append(self.q.get())
        if not frames:
            self.text.insert(tk.END, "⚠️ Inget ljud inspelat!\n")
            return

        audio = np.concatenate(frames, axis=0)
        # Spara temporär WAV
        wav_path = None
        try:
            with tempfile.NamedTemporaryFile(suffix=".wav", delete=False) as tmp:
                wav_path = tmp.name
                wf = wave.open(wav_path, "wb")
                wf.setnchannels(1)
                wf.setsampwidth(2)  # 16 bit = 2 bytes
                wf.setframerate(16000)
                # Konvertera float32 → int16
                int_data = (audio * 32767).astype(np.int16)
                wf.writeframes(int_data.tobytes())
                wf.close()

            # Skicka till Ollama för transkription
            self.text.insert(tk.END, f"Skickar till Ollama för transkription...\n")
            self.text.see(tk.END)
            
            # Använd OpenAI-kompatibelt API från Ollama
            with open(wav_path, "rb") as audio_file:
                try:
                    # Skicka filen till Ollama API
                    files = {'file': audio_file}
                    headers = {'Authorization': f'Bearer ollama'}
                    response = requests.post(
                        f"{OLLAMA_URL}/v1/audio/transcriptions",
                        files=files,
                        data={'model': self.model_name},
                        headers=headers
                    )
                    
                    if response.status_code == 200:
                        result = response.json()
                        text = result.get("text", "")
                    else:
                        # Fallback om API-anropet misslyckas
                        self.text.insert(tk.END, f"API-fel: {response.status_code} - {response.text}\n")
                        self.text.see(tk.END)
                        text = "[Kunde inte transkribera ljudet]" 
                except Exception as e:
                    self.text.insert(tk.END, f"Fel vid transkription: {str(e)}\n")
                    self.text.see(tk.END)
                    text = "[Ett fel uppstod vid transkription]"

            # Visa i GUI:t
            self.text.insert(tk.END, f"📝 Transkript: {text}\n\n")
            self.text.see(tk.END)
            
        except Exception as e:
            self.text.insert(tk.END, f"Fel: {str(e)}\n")
            self.text.see(tk.END)
        finally:
            # Ta bort temporär fil
            if wav_path and os.path.exists(wav_path):
                try:
                    os.unlink(wav_path)
                except:
                    pass

if __name__ == "__main__":
    root = tk.Tk()
    app = TranscriberApp(root)
    root.mainloop()
