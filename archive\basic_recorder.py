#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import tkinter as tk
from tkinter import ttk, messagebox
import pyaudio
import wave
import os
import time
import threading
import numpy as np

class BasicRecorder:
    def __init__(self, root):
        self.root = root
        self.root.title("Basic Audio Recorder")
        self.root.geometry("400x200")
        
        # Audio parameters
        self.chunk = 1024
        self.sample_format = pyaudio.paInt16
        self.channels = 1
        self.rate = 16000
        
        # State variables
        self.frames = []
        self.recording = False
        self.p = None
        self.stream = None
        
        # Create UI
        self.create_widgets()
    
    def create_widgets(self):
        # Main frame
        frame = ttk.Frame(self.root, padding=10)
        frame.pack(fill=tk.BOTH, expand=True)
        
        # Buttons
        self.record_button = ttk.Button(frame, text="Start Recording", command=self.toggle_recording)
        self.record_button.pack(pady=10)
        
        self.save_button = ttk.Button(frame, text="Save Recording", command=self.save_recording, state=tk.DISABLED)
        self.save_button.pack(pady=10)
        
        # Status label
        self.status_label = ttk.Label(frame, text="Ready")
        self.status_label.pack(pady=10)
        
        # Time label
        self.time_label = ttk.Label(frame, text="00:00")
        self.time_label.pack(pady=10)
    
    def toggle_recording(self):
        if not self.recording:
            self.start_recording()
        else:
            self.stop_recording()
    
    def start_recording(self):
        try:
            self.frames = []
            self.p = pyaudio.PyAudio()
            
            # Open stream
            self.stream = self.p.open(
                format=self.sample_format,
                channels=self.channels,
                rate=self.rate,
                frames_per_buffer=self.chunk,
                input=True
            )
            
            self.recording = True
            self.record_button.config(text="Stop Recording")
            self.save_button.config(state=tk.DISABLED)
            self.status_label.config(text="Recording...")
            
            # Start recording thread
            self.start_time = time.time()
            self.record_thread = threading.Thread(target=self.record)
            self.record_thread.daemon = True
            self.record_thread.start()
            
            # Start timer update
            self.update_timer()
            
        except Exception as e:
            messagebox.showerror("Error", f"Could not start recording: {str(e)}")
    
    def record(self):
        try:
            while self.recording:
                data = self.stream.read(self.chunk, exception_on_overflow=False)
                self.frames.append(data)
        except Exception as e:
            print(f"Error recording: {e}")
            self.recording = False
    
    def update_timer(self):
        if self.recording:
            elapsed = time.time() - self.start_time
            mins, secs = divmod(int(elapsed), 60)
            self.time_label.config(text=f"{mins:02d}:{secs:02d}")
            self.root.after(1000, self.update_timer)
    
    def stop_recording(self):
        if self.recording:
            self.recording = False
            
            # Stop and close the stream
            if self.stream:
                self.stream.stop_stream()
                self.stream.close()
            
            # Terminate PyAudio
            if self.p:
                self.p.terminate()
            
            self.record_button.config(text="Start Recording")
            self.save_button.config(state=tk.NORMAL if self.frames else tk.DISABLED)
            self.status_label.config(text="Recording stopped")
    
    def save_recording(self):
        if not self.frames:
            messagebox.showinfo("Info", "No audio recorded")
            return
        
        try:
            # Create output directory if it doesn't exist
            output_dir = "recordings"
            os.makedirs(output_dir, exist_ok=True)
            
            # Generate filename
            filename = os.path.join(output_dir, f"recording_{int(time.time())}.wav")
            
            # Save as WAV file
            wf = wave.open(filename, 'wb')
            wf.setnchannels(self.channels)
            wf.setsampwidth(pyaudio.PyAudio().get_sample_size(self.sample_format))
            wf.setframerate(self.rate)
            wf.writeframes(b''.join(self.frames))
            wf.close()
            
            messagebox.showinfo("Success", f"Recording saved to {filename}")
            self.status_label.config(text=f"Saved to {filename}")
            
        except Exception as e:
            messagebox.showerror("Error", f"Could not save recording: {str(e)}")

if __name__ == "__main__":
    root = tk.Tk()
    app = BasicRecorder(root)
    root.mainloop()
