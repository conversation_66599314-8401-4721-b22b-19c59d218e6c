#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import threading
import time
import logging
import os
import sys
import json
from datetime import datetime

# Import application modules
from backend.audio_capture import AudioCapture
from backend.transcription import TranscriptionService
from backend.summarization import SummarizationService
from backend.export import ExportService
from backend.database import DatabaseManager
from ui.main_window import create_main_window
from ui.settings_window import SettingsWindow
from config import Config

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("sumonthefly.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class SumOnTheFlyApp:
    """Main application class for SumOnTheFly"""
    
    def __init__(self, root):
        self.root = root
        self.root.title("SumOnTheFly")
        self.root.geometry("1024x768")
        self.root.minsize(800, 600)
        
        # Initialize configuration
        self.config = Config()
        
        # Initialize components
        self.db = DatabaseManager()
        self.audio_capture = None
        self.transcription_service = None
        self.summarization_service = None
        self.export_service = ExportService()
        
        # Initialize UI
        self.setup_ui()
        
        # State variables
        self.is_recording = False
        self.current_session_id = None
        self.transcription_buffer = ""
        self.last_summary_time = 0
        
        # Start the periodic task for updating UI
        self.update_ui()
        
    def setup_ui(self):
        """Set up the main UI components"""
        # Create main frame
        self.main_frame = ttk.Frame(self.root)
        self.main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # Create menu bar
        self.create_menu()
        
        # Create main UI components
        self.create_control_panel()
        self.create_transcription_panel()
        self.create_summary_panel()
        self.create_status_bar()
    
    def create_menu(self):
        """Create the application menu bar"""
        menubar = tk.Menu(self.root)
        
        # File menu
        file_menu = tk.Menu(menubar, tearoff=0)
        file_menu.add_command(label="New Session", command=self.new_session)
        file_menu.add_command(label="Open Session", command=self.open_session)
        file_menu.add_separator()
        file_menu.add_command(label="Export as PDF", command=lambda: self.export_session("pdf"))
        file_menu.add_command(label="Export as Word", command=lambda: self.export_session("docx"))
        file_menu.add_separator()
        file_menu.add_command(label="Exit", command=self.root.quit)
        menubar.add_cascade(label="File", menu=file_menu)
        
        # Edit menu
        edit_menu = tk.Menu(menubar, tearoff=0)
        edit_menu.add_command(label="Settings", command=self.open_settings)
        menubar.add_cascade(label="Edit", menu=edit_menu)
        
        # Help menu
        help_menu = tk.Menu(menubar, tearoff=0)
        help_menu.add_command(label="About", command=self.show_about)
        menubar.add_cascade(label="Help", menu=help_menu)
        
        self.root.config(menu=menubar)
    
    def create_control_panel(self):
        """Create the recording control panel"""
        control_frame = ttk.LabelFrame(self.main_frame, text="Controls")
        control_frame.pack(fill=tk.X, pady=5)
        
        # Recording controls
        self.record_button = ttk.Button(
            control_frame, 
            text="Start Recording", 
            command=self.toggle_recording
        )
        self.record_button.pack(side=tk.LEFT, padx=5, pady=5)
        
        # Session info
        self.session_label = ttk.Label(control_frame, text="No active session")
        self.session_label.pack(side=tk.LEFT, padx=5, pady=5)
        
        # Recording time
        self.time_label = ttk.Label(control_frame, text="00:00:00")
        self.time_label.pack(side=tk.RIGHT, padx=5, pady=5)
    
    def create_transcription_panel(self):
        """Create the transcription display panel"""
        transcription_frame = ttk.LabelFrame(self.main_frame, text="Real-time Transcription")
        transcription_frame.pack(fill=tk.BOTH, expand=True, pady=5)
        
        # Transcription text area
        self.transcription_text = tk.Text(
            transcription_frame,
            wrap=tk.WORD,
            state=tk.DISABLED
        )
        transcription_scroll = ttk.Scrollbar(
            transcription_frame, 
            command=self.transcription_text.yview
        )
        self.transcription_text.configure(yscrollcommand=transcription_scroll.set)
        
        self.transcription_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        transcription_scroll.pack(side=tk.RIGHT, fill=tk.Y)
    
    def create_summary_panel(self):
        """Create the summary display panel"""
        summary_frame = ttk.LabelFrame(self.main_frame, text="Summaries")
        summary_frame.pack(fill=tk.BOTH, expand=True, pady=5)
        
        # Summary text area
        self.summary_text = tk.Text(
            summary_frame,
            wrap=tk.WORD,
            state=tk.DISABLED,
            height=10
        )
        summary_scroll = ttk.Scrollbar(
            summary_frame, 
            command=self.summary_text.yview
        )
        self.summary_text.configure(yscrollcommand=summary_scroll.set)
        
        self.summary_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        summary_scroll.pack(side=tk.RIGHT, fill=tk.Y)
    
    def create_status_bar(self):
        """Create the status bar"""
        self.status_bar = ttk.Label(
            self.root, 
            text="Ready", 
            relief=tk.SUNKEN, 
            anchor=tk.W
        )
        self.status_bar.pack(side=tk.BOTTOM, fill=tk.X)
    
    def update_ui(self):
        """Periodically update the UI"""
        if self.is_recording:
            # Update recording time
            elapsed = time.time() - self.recording_start_time
            hours, remainder = divmod(int(elapsed), 3600)
            minutes, seconds = divmod(remainder, 60)
            self.time_label.config(text=f"{hours:02d}:{minutes:02d}:{seconds:02d}")
            
            # Check if it's time for a new summary
            summary_interval = self.config.get("summary_interval", 30)
            if elapsed - self.last_summary_time >= summary_interval and self.transcription_buffer.strip():
                self.generate_summary()
                self.last_summary_time = elapsed
        
        # Schedule the next update
        self.root.after(1000, self.update_ui)
    
    def toggle_recording(self):
        """Start or stop recording"""
        if not self.is_recording:
            self.start_recording()
        else:
            self.stop_recording()
    
    def start_recording(self):
        """Start a new recording session"""
        try:
            # Create a new session
            session_name = f"Session_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            self.current_session_id = self.db.create_session(session_name)
            
            # Initialize services
            self.audio_capture = AudioCapture(
                on_audio_chunk=self.on_audio_chunk
            )
            
            self.transcription_service = TranscriptionService(
                ollama_host=self.config.get("ollama_host", "http://localhost:11434"),
                model=self.config.get("transcription_model", "whisper"),
                on_transcription=self.on_transcription
            )
            
            self.summarization_service = SummarizationService(
                ollama_host=self.config.get("ollama_host", "http://localhost:11434"),
                model=self.config.get("summarization_model", "llama2"),
                on_summary=self.on_summary
            )
            
            # Start recording
            success = self.audio_capture.start_recording()
            if not success:
                raise Exception("Failed to start audio recording")
            
            # Update UI
            self.is_recording = True
            self.recording_start_time = time.time()
            self.last_summary_time = 0
            self.transcription_buffer = ""
            self.record_button.config(text="Stop Recording")
            self.session_label.config(text=f"Active session: {session_name}")
            self.status_bar.config(text="Recording...")
            
            # Clear text areas
            self.transcription_text.config(state=tk.NORMAL)
            self.transcription_text.delete(1.0, tk.END)
            self.transcription_text.config(state=tk.DISABLED)
            
            self.summary_text.config(state=tk.NORMAL)
            self.summary_text.delete(1.0, tk.END)
            self.summary_text.config(state=tk.DISABLED)
            
            logger.info(f"Started recording session: {session_name}")
            
        except Exception as e:
            logger.error(f"Error starting recording: {str(e)}")
            messagebox.showerror("Error", f"Failed to start recording: {str(e)}")
            self.cleanup_services()
    
    def stop_recording(self):
        """Stop the current recording session"""
        if not self.is_recording:
            return
        
        try:
            # Stop services
            if self.audio_capture:
                self.audio_capture.stop_recording()
            
            # Generate final summary if needed
            if self.transcription_buffer.strip():
                self.generate_summary()
            
            # Update UI
            self.is_recording = False
            self.record_button.config(text="Start Recording")
            self.status_bar.config(text="Ready")
            
            # Save session data
            if self.current_session_id:
                self.db.update_session_end_time(self.current_session_id)
            
            logger.info("Stopped recording session")
            
        except Exception as e:
            logger.error(f"Error stopping recording: {str(e)}")
            messagebox.showerror("Error", f"Error stopping recording: {str(e)}")
        finally:
            self.cleanup_services()
    
    def cleanup_services(self):
        """Clean up and release resources"""
        if hasattr(self, 'audio_capture') and self.audio_capture:
            self.audio_capture = None
        
        if hasattr(self, 'transcription_service') and self.transcription_service:
            self.transcription_service = None
        
        if hasattr(self, 'summarization_service') and self.summarization_service:
            self.summarization_service = None
    
    def on_audio_chunk(self, audio_chunk):
        """Handle new audio chunk from the recorder"""
        if self.transcription_service:
            self.transcription_service.process_audio(audio_chunk)
    
    def on_transcription(self, text):
        """Handle new transcription text"""
        if not text or not self.is_recording:
            return
        
        # Update the transcription buffer
        self.transcription_buffer += text + " "
        
        # Update the UI
        self.transcription_text.config(state=tk.NORMAL)
        self.transcription_text.insert(tk.END, text + " ")
        self.transcription_text.see(tk.END)
        self.transcription_text.config(state=tk.DISABLED)
        
        # Save to database
        if self.current_session_id:
            self.db.add_transcription(self.current_session_id, text, time.time())
    
    def generate_summary(self):
        """Generate a summary from the current transcription buffer"""
        if not self.summarization_service or not self.transcription_buffer.strip():
            return
        
        try:
            # Request summary asynchronously
            self.summarization_service.summarize_text(
                self.transcription_buffer,
                "Summarize the key points in 3-5 bullets."
            )
            
            # Clear the transcription buffer for the next summary period
            self.transcription_buffer = ""
            
        except Exception as e:
            logger.error(f"Error generating summary: {str(e)}")
            self.status_bar.config(text=f"Error generating summary: {str(e)}")
    
    def on_summary(self, summary):
        """Handle new summary"""
        if not summary or not self.is_recording:
            return
        
        # Format the summary with timestamp
        timestamp = datetime.now().strftime("%H:%M:%S")
        formatted_summary = f"\n[{timestamp}]\n{summary}\n"
        
        # Update the UI
        self.summary_text.config(state=tk.NORMAL)
        self.summary_text.insert(tk.END, formatted_summary)
        self.summary_text.see(tk.END)
        self.summary_text.config(state=tk.DISABLED)
        
        # Save to database
        if self.current_session_id:
            self.db.add_summary(self.current_session_id, summary, time.time())
    
    def new_session(self):
        """Start a new session"""
        if self.is_recording:
            if messagebox.askyesno("Confirm", "Stop current recording and start a new session?"):
                self.stop_recording()
                self.start_recording()
        else:
            self.start_recording()
    
    def open_session(self):
        """Open a previous session"""
        # TODO: Implement session loading functionality
        messagebox.showinfo("Info", "Session loading not implemented yet")
    
    def export_session(self, format_type):
        """Export the current session to a file"""
        if not self.current_session_id:
            messagebox.showinfo("Info", "No active session to export")
            return
        
        try:
            # Get session data from database
            session_data = self.db.get_session_data(self.current_session_id)
            
            # Ask for save location
            file_types = [("PDF files", "*.pdf")] if format_type == "pdf" else [("Word files", "*.docx")]
            filename = filedialog.asksaveasfilename(
                defaultextension=f".{format_type}",
                filetypes=file_types
            )
            
            if not filename:
                return
            
            # Export the file
            if format_type == "pdf":
                self.export_service.export_to_pdf(filename, session_data)
            else:
                self.export_service.export_to_docx(filename, session_data)
            
            messagebox.showinfo("Success", f"Session exported to {filename}")
            
        except Exception as e:
            logger.error(f"Error exporting session: {str(e)}")
            messagebox.showerror("Error", f"Failed to export session: {str(e)}")
    
    def open_settings(self):
        """Open the settings window"""
        settings_window = SettingsWindow(self.root, self.config)
    
    def show_about(self):
        """Show the about dialog"""
        about_text = """SumOnTheFly v1.0
        
A desktop application that records audio, transcribes it in real-time, and generates bullet-point summaries.

Developed with Python and Tkinter.
Uses Ollama for local AI processing.
        """
        messagebox.showinfo("About SumOnTheFly", about_text)


def main():
    """Main entry point for the application"""
    try:
        # Create the root window
        root = tk.Tk()
        
        # Set application icon
        # if os.path.exists("assets/icon.ico"):
        #     root.iconbitmap("assets/icon.ico")
        
        # Create and run the application
        app = SumOnTheFlyApp(root)
        root.mainloop()
        
    except Exception as e:
        logger.critical(f"Unhandled exception: {str(e)}")
        messagebox.showerror("Critical Error", f"An unhandled error occurred: {str(e)}")
        return 1
    
    return 0


if __name__ == "__main__":
    sys.exit(main())
