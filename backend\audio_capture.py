import logging
import threading
import time
import numpy as np
from typing import Optional, Callable, Dict, Any, List
import os
import sys

logger = logging.getLogger(__name__)

# Try to import audio libraries but don't fail if they're not available
try:
    import pyaudio
    PYAUDIO_AVAILABLE = True
except ImportError:
    logger.warning("PyAudio not available. Some audio capture features will be limited.")
    PYAUDIO_AVAILABLE = False

try:
    import sounddevice as sd
    SOUNDDEVICE_AVAILABLE = True
except ImportError:
    logger.warning("sounddevice not available. System audio capture will be disabled.")
    SOUNDDEVICE_AVAILABLE = False

try:
    import wave
    WAVE_AVAILABLE = True
except ImportError:
    logger.warning("wave module not available. Audio export will be disabled.")
    WAVE_AVAILABLE = False

class AudioCapture:
    """
    Captures audio from both microphone and system audio simultaneously.
    Provides real-time audio data for transcription.
    """
    
    def __init__(self, 
                 sample_rate: int = 16000, 
                 chunk_size: int = 1024,
                 channels: int = 1,
                 on_audio_chunk: Optional[Callable[[np.ndarray], None]] = None,
                 input_device_index: Optional[int] = None,
                 input_device_name: Optional[str] = None):
        self.sample_rate = sample_rate
        self.chunk_size = chunk_size
        self.channels = channels
        self.on_audio_chunk = on_audio_chunk
        self.is_recording = False
        self.audio_buffer = []
        self.lock = threading.Lock()
        self.recording_thread = None
        self.system_audio_thread = None
        self.input_device_index = input_device_index
        self.input_device_name = input_device_name
        self.use_test_audio = False
        
        # Initialize PyAudio if available
        self.pyaudio_instance = None
        if PYAUDIO_AVAILABLE:
            try:
                self.pyaudio_instance = pyaudio.PyAudio()
                self._log_available_devices()
            except Exception as e:
                logger.error(f"Failed to initialize PyAudio: {str(e)}")
        else:
            # If PyAudio is not available, use test audio
            self.use_test_audio = True
            logger.warning("Using test audio generation instead of real audio capture")
    
    def _log_available_devices(self):
        """Log information about available audio devices"""
        if not PYAUDIO_AVAILABLE or not self.pyaudio_instance:
            return
            
        try:
            logger.info(f"Available audio devices:")
            device_count = self.pyaudio_instance.get_device_count()
            default_input = self.pyaudio_instance.get_default_input_device_info()['index'] if self.pyaudio_instance.get_default_input_device_info() else "None"
            
            logger.info(f"Found {device_count} audio devices. Default input: {default_input}")
            
            for i in range(device_count):
                try:
                    device_info = self.pyaudio_instance.get_device_info_by_index(i)
                    input_channels = device_info.get('maxInputChannels', 0)
                    if input_channels > 0:
                        logger.info(f"Device {i}: {device_info.get('name')} (Input Channels: {input_channels})")
                except Exception as e:
                    logger.warning(f"Error getting info for device {i}: {str(e)}")
        except Exception as e:
            logger.error(f"Error logging audio devices: {str(e)}")
    
    def _find_input_device(self) -> int:
        """Find the appropriate input device based on name or default to system default"""
        if not PYAUDIO_AVAILABLE or not self.pyaudio_instance:
            return -1
            
        # If a specific device index was provided, use it
        if self.input_device_index is not None:
            try:
                device_info = self.pyaudio_instance.get_device_info_by_index(self.input_device_index)
                if device_info.get('maxInputChannels', 0) > 0:
                    logger.info(f"Using specified input device {self.input_device_index}: {device_info.get('name')}")
                    return self.input_device_index
                else:
                    logger.warning(f"Specified device {self.input_device_index} has no input channels")
            except Exception as e:
                logger.warning(f"Error using specified device {self.input_device_index}: {str(e)}")
        
        # If a device name was provided, try to find it
        if self.input_device_name and self.input_device_name.lower() != "default":
            try:
                device_count = self.pyaudio_instance.get_device_count()
                for i in range(device_count):
                    try:
                        device_info = self.pyaudio_instance.get_device_info_by_index(i)
                        if (self.input_device_name.lower() in device_info.get('name', '').lower() and 
                            device_info.get('maxInputChannels', 0) > 0):
                            logger.info(f"Found device matching '{self.input_device_name}': {device_info.get('name')} (index: {i})")
                            return i
                    except Exception:
                        continue
                logger.warning(f"No device found matching name '{self.input_device_name}'")
            except Exception as e:
                logger.warning(f"Error searching for device by name: {str(e)}")
        
        # Fall back to default input device
        try:
            default_device = self.pyaudio_instance.get_default_input_device_info()
            logger.info(f"Using default input device: {default_device.get('name')} (index: {default_device.get('index')})")
            return default_device.get('index')
        except Exception as e:
            logger.error(f"Error getting default input device: {str(e)}")
            
            # Last resort: try to find any input device
            try:
                device_count = self.pyaudio_instance.get_device_count()
                for i in range(device_count):
                    try:
                        device_info = self.pyaudio_instance.get_device_info_by_index(i)
                        if device_info.get('maxInputChannels', 0) > 0:
                            logger.info(f"Using first available input device: {device_info.get('name')} (index: {i})")
                            return i
                    except Exception:
                        continue
            except Exception:
                pass
                
            # If we get here, no input devices were found
            logger.error("No input devices found. Using test audio generation.")
            self.use_test_audio = True
            return -1
    
    def start_recording(self) -> bool:
        """Start recording from microphone and system audio"""
        if self.is_recording:
            logger.warning("Recording is already in progress")
            return False
        
        try:
            self.is_recording = True
            self.audio_buffer = []
            
            # Start appropriate recording thread
            if self.use_test_audio:
                # Use test audio generation
                self.recording_thread = threading.Thread(
                    target=self._generate_test_audio,
                    daemon=True
                )
                self.recording_thread.start()
                logger.info("Started generating test audio data")
            elif PYAUDIO_AVAILABLE and self.pyaudio_instance:
                # Use real microphone recording
                self.recording_thread = threading.Thread(
                    target=self._record_microphone,
                    daemon=True
                )
                self.recording_thread.start()
                logger.info("Started recording audio from microphone")
            else:
                # Fallback to test audio
                self.recording_thread = threading.Thread(
                    target=self._generate_test_audio,
                    daemon=True
                )
                self.recording_thread.start()
                logger.info("Started generating test audio data (fallback)")
            
            # Try to start system audio recording if available
            if SOUNDDEVICE_AVAILABLE and not self.use_test_audio:
                try:
                    self.system_audio_thread = threading.Thread(
                        target=self._record_system_audio,
                        daemon=True
                    )
                    self.system_audio_thread.start()
                    logger.info("Started recording system audio")
                except Exception as e:
                    logger.warning(f"Could not start system audio recording: {str(e)}")
            
            return True
        except Exception as e:
            logger.error(f"Failed to start recording: {str(e)}")
            self.is_recording = False
            return False
    
    def stop_recording(self) -> bool:
        """Stop all audio recording"""
        if not self.is_recording:
            logger.warning("No recording in progress")
            return False
        
        self.is_recording = False
        
        # Wait for threads to finish
        if self.recording_thread and self.recording_thread.is_alive():
            self.recording_thread.join(timeout=2.0)
        
        if self.system_audio_thread and self.system_audio_thread.is_alive():
            self.system_audio_thread.join(timeout=2.0)
        
        logger.info("Stopped recording audio")
        return True
    
    def _record_microphone(self):
        """Record audio from microphone using PyAudio"""
        if not PYAUDIO_AVAILABLE or not self.pyaudio_instance:
            logger.error("PyAudio is not available")
            self.use_test_audio = True
            self._generate_test_audio()
            return
            
        try:
            # Find appropriate input device
            device_index = self._find_input_device()
            if device_index < 0:
                logger.error("No suitable input device found")
                self.use_test_audio = True
                self._generate_test_audio()
                return
                
            # Open audio stream
            stream = self.pyaudio_instance.open(
                format=pyaudio.paInt16,
                channels=self.channels,
                rate=self.sample_rate,
                input=True,
                input_device_index=device_index,
                frames_per_buffer=self.chunk_size
            )
            
            logger.info(f"Microphone stream opened successfully with device index {device_index}")
            
            while self.is_recording:
                try:
                    audio_data = stream.read(self.chunk_size, exception_on_overflow=False)
                    audio_array = np.frombuffer(audio_data, dtype=np.int16)
                    
                    with self.lock:
                        self.audio_buffer.append(audio_array)
                    
                    if self.on_audio_chunk:
                        self.on_audio_chunk(audio_array)
                except Exception as e:
                    logger.warning(f"Error reading from microphone: {str(e)}")
                    time.sleep(0.1)  # Avoid tight loop if there are persistent errors
            
            # Clean up
            try:
                stream.stop_stream()
                stream.close()
            except Exception as e:
                logger.warning(f"Error closing audio stream: {str(e)}")
                
        except Exception as e:
            logger.error(f"Error in microphone recording: {str(e)}")
            self.is_recording = False
            self.use_test_audio = True
            self._generate_test_audio()
    
    def _record_system_audio(self):
        """Record system audio using sounddevice"""
        if not SOUNDDEVICE_AVAILABLE:
            logger.error("sounddevice is not available")
            return
            
        try:
            # Log available devices
            devices = sd.query_devices()
            logger.info(f"Available sounddevice devices:")
            for i, device in enumerate(devices):
                logger.info(f"SD Device {i}: {device['name']} (Inputs: {device.get('max_input_channels', 0)}, Outputs: {device.get('max_output_channels', 0)})")
            
            # Define callback for sounddevice
            def callback(indata, frames, time, status):
                if status:
                    logger.warning(f"System audio capture status: {status}")
                
                if self.is_recording:
                    try:
                        # Convert to int16 format
                        audio_array = np.array(indata[:, 0] * 32767, dtype=np.int16)
                        
                        with self.lock:
                            self.audio_buffer.append(audio_array)
                        
                        if self.on_audio_chunk:
                            self.on_audio_chunk(audio_array)
                    except Exception as e:
                        logger.warning(f"Error processing system audio: {str(e)}")
            
            # Try to find a loopback device for system audio
            loopback_device = None
            
            for i, device in enumerate(devices):
                device_name = device['name'].lower()
                if ('loopback' in device_name or 
                    'output' in device_name or 
                    'mix' in device_name or 
                    'what u hear' in device_name or
                    'stereo mix' in device_name):
                    if device.get('max_input_channels', 0) > 0:
                        loopback_device = i
                        logger.info(f"Found potential loopback device: {device['name']} (index: {i})")
                        break
            
            if loopback_device is None:
                # Try default input as fallback
                try:
                    default_device = sd.default.device[0]
                    device_info = devices[default_device]
                    if device_info.get('max_input_channels', 0) > 0:
                        loopback_device = default_device
                        logger.info(f"Using default input for system audio: {device_info['name']} (index: {default_device})")
                    else:
                        logger.warning("Default input device has no input channels")
                        return
                except Exception as e:
                    logger.warning(f"Error getting default input device: {str(e)}")
                    return
            
            # Start the input stream
            with sd.InputStream(
                device=loopback_device,
                channels=self.channels,
                samplerate=self.sample_rate,
                blocksize=self.chunk_size,
                callback=callback
            ):
                logger.info(f"System audio stream started with device index {loopback_device}")
                while self.is_recording:
                    time.sleep(0.1)
                    
        except Exception as e:
            logger.error(f"Error in system audio recording: {str(e)}")
            self.is_recording = False
    
    def _generate_test_audio(self):
        """Generate synthetic audio data for testing when no audio capture is available"""
        logger.info("Generating synthetic audio for testing")
        
        try:
            # Generate a simple sine wave
            frequency = 440  # 440 Hz = A4 note
            
            sample_index = 0
            while self.is_recording:
                # Generate chunk_size samples of a sine wave
                t = np.arange(sample_index, sample_index + self.chunk_size) / self.sample_rate
                audio_array = np.sin(2 * np.pi * frequency * t) * 32767 / 4  # Quarter amplitude
                audio_array = audio_array.astype(np.int16)
                
                with self.lock:
                    self.audio_buffer.append(audio_array)
                
                if self.on_audio_chunk:
                    self.on_audio_chunk(audio_array)
                
                sample_index += self.chunk_size
                time.sleep(self.chunk_size / self.sample_rate)  # Sleep for the duration of the chunk
                
        except Exception as e:
            logger.error(f"Error generating test audio: {str(e)}")
            self.is_recording = False
    
    def get_audio_data(self) -> np.ndarray:
        """Get all recorded audio data as a single numpy array"""
        with self.lock:
            if not self.audio_buffer:
                return np.array([], dtype=np.int16)
            
            # Combine all chunks in the buffer
            combined = np.concatenate(self.audio_buffer)
            return combined
    
    def save_to_file(self, filename: str) -> bool:
        """Save recorded audio to a WAV file"""
        if not WAVE_AVAILABLE:
            logger.error("wave module is not available. Cannot save audio to file.")
            return False
            
        try:
            audio_data = self.get_audio_data()
            
            if len(audio_data) == 0:
                logger.warning("No audio data to save")
                return False
            
            with wave.open(filename, 'wb') as wf:
                wf.setnchannels(self.channels)
                if PYAUDIO_AVAILABLE and self.pyaudio_instance:
                    wf.setsampwidth(self.pyaudio_instance.get_sample_size(pyaudio.paInt16))
                else:
                    wf.setsampwidth(2)  # 16-bit audio = 2 bytes
                wf.setframerate(self.sample_rate)
                wf.writeframes(audio_data.tobytes())
            
            logger.info(f"Saved audio to {filename}")
            return True
        except Exception as e:
            logger.error(f"Failed to save audio to file: {str(e)}")
            return False
    
    def get_available_devices(self) -> List[Dict[str, Any]]:
        """Get a list of available audio input devices"""
        devices = []
        
        if PYAUDIO_AVAILABLE and self.pyaudio_instance:
            try:
                device_count = self.pyaudio_instance.get_device_count()
                for i in range(device_count):
                    try:
                        device_info = self.pyaudio_instance.get_device_info_by_index(i)
                        if device_info.get('maxInputChannels', 0) > 0:
                            devices.append({
                                'index': i,
                                'name': device_info.get('name', f"Device {i}"),
                                'channels': device_info.get('maxInputChannels', 0),
                                'default': i == self.pyaudio_instance.get_default_input_device_info().get('index', -1)
                            })
                    except Exception as e:
                        logger.warning(f"Error getting info for device {i}: {str(e)}")
            except Exception as e:
                logger.error(f"Error getting audio devices: {str(e)}")
        
        # Add a test audio option
        devices.append({
            'index': -1,
            'name': "Test Audio (Simulated)",
            'channels': 1,
            'default': False
        })
        
        return devices
    
    def __del__(self):
        """Clean up resources"""
        self.stop_recording()
        if PYAUDIO_AVAILABLE and hasattr(self, 'pyaudio_instance') and self.pyaudio_instance:
            self.pyaudio_instance.terminate()


# Example usage
if __name__ == "__main__":
    logging.basicConfig(level=logging.INFO)
    
    def on_chunk(audio_chunk):
        # This would be connected to the transcription service
        print(f"Received audio chunk: {len(audio_chunk)} samples")
    
    recorder = AudioCapture(on_audio_chunk=on_chunk)
    
    # Print available devices
    devices = recorder.get_available_devices()
    print("Available input devices:")
    for device in devices:
        print(f"  {device['index']}: {device['name']} (Channels: {device['channels']})" + 
              (" (Default)" if device['default'] else ""))
    
    print("\nStarting recording for 5 seconds...")
    recorder.start_recording()
    time.sleep(5)
    recorder.stop_recording()
    
    # Save the recording
    recorder.save_to_file("test_recording.wav")
    print("Recording saved to test_recording.wav")
