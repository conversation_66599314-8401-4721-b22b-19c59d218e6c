import os
import logging
from datetime import datetime
from typing import Dict, List, Any, Optional

logger = logging.getLogger(__name__)

# Try to import PDF generation libraries
try:
    from reportlab.lib.pagesizes import letter
    from reportlab.lib import colors
    from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
    from reportlab.platypus import SimpleD<PERSON><PERSON>emplate, Paragraph, Spacer, Table, TableStyle
    REPORTLAB_AVAILABLE = True
except ImportError:
    logger.warning("ReportLab not available. PDF export will be disabled.")
    REPORTLAB_AVAILABLE = False

# Try to import Word document generation libraries
try:
    from docx import Document
    from docx.shared import Inches, Pt
    from docx.enum.text import WD_ALIGN_PARAGRAPH
    DOCX_AVAILABLE = True
except ImportError:
    logger.warning("python-docx not available. Word export will be disabled.")
    DOCX_AVAILABLE = False

class ExportService:
    """
    Service for exporting session data to PDF and Word documents.
    """
    
    def __init__(self):
        self.styles = None
        
        # Initialize styles if ReportLab is available
        if REPORTLAB_AVAILABLE:
            self.styles = getSampleStyleSheet()
            
            # Create custom styles for PDF - check if they already exist first
            if 'CustomHeading1' not in self.styles:
                self.styles.add(ParagraphStyle(
                    name='CustomHeading1',
                    parent=self.styles['Heading1'],
                    fontSize=16,
                    spaceAfter=12
                ))
            
            if 'CustomHeading2' not in self.styles:
                self.styles.add(ParagraphStyle(
                    name='CustomHeading2',
                    parent=self.styles['Heading2'],
                    fontSize=14,
                    spaceAfter=10
                ))
            
            if 'CustomNormal' not in self.styles:
                self.styles.add(ParagraphStyle(
                    name='CustomNormal',
                    parent=self.styles['Normal'],
                    fontSize=11,
                    spaceAfter=8
                ))
            
            if 'CustomBullet' not in self.styles:
                self.styles.add(ParagraphStyle(
                    name='CustomBullet',
                    parent=self.styles['Normal'],
                    fontSize=11,
                    leftIndent=20,
                    firstLineIndent=-15,
                    spaceAfter=5
                ))
    
    def export_to_pdf(self, filename: str, session_data: Dict[str, Any]) -> bool:
        """Export session data to a PDF file"""
        if not REPORTLAB_AVAILABLE:
            logger.error("Cannot export to PDF: ReportLab library not available")
            return False
            
        try:
            # Extract session information
            session = session_data.get('session', {})
            transcriptions = session_data.get('transcriptions', [])
            summaries = session_data.get('summaries', [])
            
            if not session:
                logger.error("No session data to export")
                return False
            
            # Create the PDF document
            doc = SimpleDocTemplate(filename, pagesize=letter)
            story = []
            
            # Add title
            title = f"SumOnTheFly Session: {session.get('name', 'Unnamed Session')}"
            story.append(Paragraph(title, self.styles['Title']))
            story.append(Spacer(1, 12))
            
            # Add session information
            start_time = datetime.fromtimestamp(session.get('start_time', 0))
            end_time = datetime.fromtimestamp(session.get('end_time', 0)) if session.get('end_time') else None
            
            session_info = [
                ["Start Time:", start_time.strftime("%Y-%m-%d %H:%M:%S")],
                ["End Time:", end_time.strftime("%Y-%m-%d %H:%M:%S") if end_time else "N/A"],
                ["Duration:", self._format_duration(start_time, end_time) if end_time else "N/A"]
            ]
            
            session_table = Table(session_info, colWidths=[100, 300])
            session_table.setStyle(TableStyle([
                ('BACKGROUND', (0, 0), (0, -1), colors.lightgrey),
                ('TEXTCOLOR', (0, 0), (0, -1), colors.black),
                ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
                ('FONTNAME', (0, 0), (-1, -1), 'Helvetica'),
                ('FONTSIZE', (0, 0), (-1, -1), 10),
                ('BOTTOMPADDING', (0, 0), (-1, -1), 6),
                ('TOPPADDING', (0, 0), (-1, -1), 6),
                ('GRID', (0, 0), (-1, -1), 0.5, colors.grey)
            ]))
            
            story.append(session_table)
            story.append(Spacer(1, 12))
            
            # Add summaries section
            if summaries:
                story.append(Paragraph("Summaries", self.styles['CustomHeading1']))
                story.append(Spacer(1, 6))
                
                for i, summary in enumerate(summaries):
                    timestamp = datetime.fromtimestamp(summary.get('timestamp', 0))
                    story.append(Paragraph(
                        f"Summary {i+1} - {timestamp.strftime('%H:%M:%S')}", 
                        self.styles['CustomHeading2']
                    ))
                    
                    # Process bullet points
                    summary_text = summary.get('text', '')
                    for line in summary_text.split('\n'):
                        line = line.strip()
                        if line.startswith('-') or line.startswith('u2022'):
                            # This is a bullet point
                            story.append(Paragraph(line, self.styles['CustomBullet']))
                        elif line:  # Skip empty lines
                            story.append(Paragraph(line, self.styles['CustomNormal']))
                    
                    story.append(Spacer(1, 12))
            
            # Add transcriptions section
            if transcriptions:
                story.append(Paragraph("Full Transcription", self.styles['CustomHeading1']))
                story.append(Spacer(1, 6))
                
                # Combine transcriptions into a single text with timestamps
                full_text = ""
                for i, trans in enumerate(transcriptions):
                    timestamp = datetime.fromtimestamp(trans.get('timestamp', 0))
                    if i > 0 and i % 5 == 0:  # Add timestamp every 5 segments
                        full_text += f"[{timestamp.strftime('%H:%M:%S')}] "
                    full_text += trans.get('text', '') + " "
                
                # Split into paragraphs for better readability
                paragraphs = self._split_into_paragraphs(full_text)
                for para in paragraphs:
                    story.append(Paragraph(para, self.styles['CustomNormal']))
                    story.append(Spacer(1, 6))
            
            # Build the PDF
            doc.build(story)
            logger.info(f"Exported session to PDF: {filename}")
            return True
            
        except Exception as e:
            logger.error(f"Error exporting to PDF: {str(e)}")
            return False
    
    def export_to_docx(self, filename: str, session_data: Dict[str, Any]) -> bool:
        """Export session data to a Word document"""
        if not DOCX_AVAILABLE:
            logger.error("Cannot export to Word: python-docx library not available")
            return False
            
        try:
            # Extract session information
            session = session_data.get('session', {})
            transcriptions = session_data.get('transcriptions', [])
            summaries = session_data.get('summaries', [])
            
            if not session:
                logger.error("No session data to export")
                return False
            
            # Create the Word document
            doc = Document()
            
            # Add title
            title = f"SumOnTheFly Session: {session.get('name', 'Unnamed Session')}"
            doc.add_heading(title, level=0)
            
            # Add session information
            start_time = datetime.fromtimestamp(session.get('start_time', 0))
            end_time = datetime.fromtimestamp(session.get('end_time', 0)) if session.get('end_time') else None
            
            session_info = doc.add_paragraph()
            session_info.add_run("Start Time: ").bold = True
            session_info.add_run(start_time.strftime("%Y-%m-%d %H:%M:%S"))
            session_info.add_run("\nEnd Time: ").bold = True
            session_info.add_run(end_time.strftime("%Y-%m-%d %H:%M:%S") if end_time else "N/A")
            session_info.add_run("\nDuration: ").bold = True
            session_info.add_run(self._format_duration(start_time, end_time) if end_time else "N/A")
            
            doc.add_paragraph()
            
            # Add summaries section
            if summaries:
                doc.add_heading("Summaries", level=1)
                
                for i, summary in enumerate(summaries):
                    timestamp = datetime.fromtimestamp(summary.get('timestamp', 0))
                    doc.add_heading(f"Summary {i+1} - {timestamp.strftime('%H:%M:%S')}", level=2)
                    
                    # Process bullet points
                    summary_text = summary.get('text', '')
                    for line in summary_text.split('\n'):
                        line = line.strip()
                        if line.startswith('-') or line.startswith('u2022'):
                            # This is a bullet point
                            p = doc.add_paragraph(line[1:].strip(), style='ListBullet')
                        elif line:  # Skip empty lines
                            doc.add_paragraph(line)
                    
                    doc.add_paragraph()
            
            # Add transcriptions section
            if transcriptions:
                doc.add_heading("Full Transcription", level=1)
                
                # Combine transcriptions into a single text with timestamps
                full_text = ""
                for i, trans in enumerate(transcriptions):
                    timestamp = datetime.fromtimestamp(trans.get('timestamp', 0))
                    if i > 0 and i % 5 == 0:  # Add timestamp every 5 segments
                        full_text += f"[{timestamp.strftime('%H:%M:%S')}] "
                    full_text += trans.get('text', '') + " "
                
                # Split into paragraphs for better readability
                paragraphs = self._split_into_paragraphs(full_text)
                for para in paragraphs:
                    doc.add_paragraph(para)
            
            # Save the document
            doc.save(filename)
            logger.info(f"Exported session to Word: {filename}")
            return True
            
        except Exception as e:
            logger.error(f"Error exporting to Word: {str(e)}")
            return False
    
    def export_to_text(self, filename: str, session_data: Dict[str, Any]) -> bool:
        """Export session data to a plain text file (fallback option)"""
        try:
            # Extract session information
            session = session_data.get('session', {})
            transcriptions = session_data.get('transcriptions', [])
            summaries = session_data.get('summaries', [])
            
            if not session:
                logger.error("No session data to export")
                return False
            
            with open(filename, 'w', encoding='utf-8') as f:
                # Write title
                title = f"SumOnTheFly Session: {session.get('name', 'Unnamed Session')}"
                f.write(f"{title}\n{'=' * len(title)}\n\n")
                
                # Write session information
                start_time = datetime.fromtimestamp(session.get('start_time', 0))
                end_time = datetime.fromtimestamp(session.get('end_time', 0)) if session.get('end_time') else None
                
                f.write(f"Start Time: {start_time.strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write(f"End Time: {end_time.strftime('%Y-%m-%d %H:%M:%S') if end_time else 'N/A'}\n")
                f.write(f"Duration: {self._format_duration(start_time, end_time) if end_time else 'N/A'}\n\n")
                
                # Write summaries
                if summaries:
                    f.write("SUMMARIES\n=========\n\n")
                    
                    for i, summary in enumerate(summaries):
                        timestamp = datetime.fromtimestamp(summary.get('timestamp', 0))
                        f.write(f"Summary {i+1} - {timestamp.strftime('%H:%M:%S')}\n")
                        f.write(f"{'-' * 30}\n")
                        
                        summary_text = summary.get('text', '')
                        f.write(f"{summary_text}\n\n")
                
                # Write transcriptions
                if transcriptions:
                    f.write("FULL TRANSCRIPTION\n==================\n\n")
                    
                    full_text = ""
                    for i, trans in enumerate(transcriptions):
                        timestamp = datetime.fromtimestamp(trans.get('timestamp', 0))
                        if i > 0 and i % 5 == 0:  # Add timestamp every 5 segments
                            full_text += f"[{timestamp.strftime('%H:%M:%S')}] "
                        full_text += trans.get('text', '') + " "
                    
                    # Split into paragraphs for better readability
                    paragraphs = self._split_into_paragraphs(full_text)
                    for para in paragraphs:
                        f.write(f"{para}\n\n")
            
            logger.info(f"Exported session to text file: {filename}")
            return True
            
        except Exception as e:
            logger.error(f"Error exporting to text file: {str(e)}")
            return False
    
    def export_session(self, filename: str, session_data: Dict[str, Any]) -> bool:
        """Export session data to the appropriate format based on file extension"""
        _, ext = os.path.splitext(filename)
        ext = ext.lower()
        
        if ext == '.pdf' and REPORTLAB_AVAILABLE:
            return self.export_to_pdf(filename, session_data)
        elif ext in ['.docx', '.doc'] and DOCX_AVAILABLE:
            return self.export_to_docx(filename, session_data)
        elif ext == '.txt' or not (REPORTLAB_AVAILABLE or DOCX_AVAILABLE):
            # Use text as fallback format
            if ext != '.txt':
                filename = f"{os.path.splitext(filename)[0]}.txt"
                logger.warning(f"Export libraries not available. Falling back to text format: {filename}")
            return self.export_to_text(filename, session_data)
        else:
            logger.error(f"Unsupported export format: {ext}")
            return False
    
    def _format_duration(self, start_time: datetime, end_time: Optional[datetime]) -> str:
        """Format the duration between start and end time"""
        if not end_time:
            return "N/A"
        
        duration_seconds = (end_time - start_time).total_seconds()
        hours, remainder = divmod(int(duration_seconds), 3600)
        minutes, seconds = divmod(remainder, 60)
        
        return f"{hours:02d}:{minutes:02d}:{seconds:02d}"
    
    def _split_into_paragraphs(self, text: str, max_length: int = 500) -> List[str]:
        """Split a long text into paragraphs of reasonable length"""
        words = text.split()
        paragraphs = []
        current_paragraph = ""
        
        for word in words:
            if len(current_paragraph) + len(word) + 1 > max_length:
                paragraphs.append(current_paragraph.strip())
                current_paragraph = word + " "
            else:
                current_paragraph += word + " "
        
        if current_paragraph.strip():
            paragraphs.append(current_paragraph.strip())
        
        return paragraphs


# Example usage
if __name__ == "__main__":
    logging.basicConfig(level=logging.INFO)
    
    # Create export service
    export_service = ExportService()
    
    # Example session data
    session_data = {
        "session": {
            "name": "Test Session",
            "start_time": datetime.now().timestamp() - 3600,  # 1 hour ago
            "end_time": datetime.now().timestamp()
        },
        "transcriptions": [
            {"text": "This is a test transcription.", "timestamp": datetime.now().timestamp() - 3500},
            {"text": "This is another test transcription.", "timestamp": datetime.now().timestamp() - 3400}
        ],
        "summaries": [
            {"text": "- First test point\n- Second test point", "timestamp": datetime.now().timestamp() - 3300}
        ]
    }
    
    # Export to various formats
    export_service.export_session("test_export.pdf", session_data)
    export_service.export_session("test_export.docx", session_data)
    export_service.export_session("test_export.txt", session_data)
