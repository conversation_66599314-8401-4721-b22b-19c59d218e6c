import tkinter as tk
from tkinter import ttk
import logging

logger = logging.getLogger(__name__)

def create_main_window(root):
    """
    Create the main application window layout.
    This is a helper function used by the main application class.
    """
    # Set window properties
    root.title("SumOnTheFly")
    root.geometry("1024x768")
    
    # Configure the grid layout
    root.columnconfigure(0, weight=1)
    root.rowconfigure(0, weight=1)
    
    # Create the main frame
    main_frame = ttk.Frame(root, padding="10")
    main_frame.grid(row=0, column=0, sticky="nsew")
    
    # Configure the main frame's grid
    main_frame.columnconfigure(0, weight=1)
    main_frame.rowconfigure(0, weight=0)  # Control panel
    main_frame.rowconfigure(1, weight=3)  # Transcription panel
    main_frame.rowconfigure(2, weight=1)  # Summary panel
    
    # Create a style for the application
    style = ttk.Style()
    style.configure("TFrame", background="#f0f0f0")
    style.configure("TLabel", background="#f0f0f0", font=("Segoe UI", 10))
    style.configure("TButton", font=("Segoe UI", 10))
    style.configure("Header.TLabel", font=("Segoe UI", 12, "bold"))
    
    return main_frame


def create_scrollable_text(parent, height=10, readonly=True):
    """
    Create a scrollable text widget with a scrollbar.
    
    Args:
        parent: The parent widget
        height: Height of the text widget in lines
        readonly: Whether the text widget should be read-only
    
    Returns:
        tuple: (frame, text_widget)
    """
    frame = ttk.Frame(parent)
    
    # Create a text widget with a scrollbar
    text_widget = tk.Text(
        frame,
        wrap=tk.WORD,
        height=height,
        font=("Segoe UI", 10)
    )
    
    scrollbar = ttk.Scrollbar(
        frame,
        orient="vertical",
        command=text_widget.yview
    )
    
    text_widget.configure(yscrollcommand=scrollbar.set)
    
    # Pack the widgets
    text_widget.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
    scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
    
    # Make the text widget read-only if specified
    if readonly:
        text_widget.config(state=tk.DISABLED)
    
    return frame, text_widget


def append_to_text(text_widget, content, clear=False):
    """
    Append content to a text widget, handling the widget state.
    
    Args:
        text_widget: The text widget to append to
        content: The content to append
        clear: Whether to clear the widget before appending
    """
    # Enable editing
    text_widget.config(state=tk.NORMAL)
    
    # Clear if requested
    if clear:
        text_widget.delete(1.0, tk.END)
    
    # Append content
    text_widget.insert(tk.END, content)
    
    # Scroll to the end
    text_widget.see(tk.END)
    
    # Disable editing
    text_widget.config(state=tk.DISABLED)
