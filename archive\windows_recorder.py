#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import threading
import time
import os
import subprocess
import json
import requests
import logging
import sys
from datetime import datetime
import wave
import numpy as np

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("windows_recorder.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class Config:
    """Configuration manager"""
    
    def __init__(self, config_file="config.json"):
        self.config_file = config_file
        self.config = {
            # Ollama settings
            "ollama_host": "http://CarlsMacStudio.got.volvo.net:11434",
            "transcription_model": "dimavz/whisper-tiny",
            "summarization_model": "qwen3",
            
            # Application settings
            "summary_interval": 30,  # seconds
            "temp_dir": os.path.join(os.path.expanduser("~"), "SumOnTheFly", "temp")
        }
        self.load()
        
        # Ensure temp directory exists
        os.makedirs(self.config["temp_dir"], exist_ok=True)
    
    def load(self):
        """Load configuration from file"""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r') as f:
                    loaded_config = json.load(f)
                    self.config.update(loaded_config)
                logger.info(f"Loaded configuration from {self.config_file}")
        except Exception as e:
            logger.error(f"Error loading configuration: {str(e)}")
    
    def save(self):
        """Save configuration to file"""
        try:
            with open(self.config_file, 'w') as f:
                json.dump(self.config, f, indent=4)
            logger.info(f"Saved configuration to {self.config_file}")
            return True
        except Exception as e:
            logger.error(f"Error saving configuration: {str(e)}")
            return False
    
    def get(self, key, default=None):
        """Get a configuration value"""
        return self.config.get(key, default)
    
    def set(self, key, value):
        """Set a configuration value"""
        self.config[key] = value

class TranscriptionService:
    """Service for transcribing audio using Ollama"""
    
    def __init__(self, ollama_host, model="whisper"):
        self.ollama_host = ollama_host
        self.model = model
        self.test_connection()
    
    def test_connection(self):
        """Test connection to Ollama server"""
        try:
            response = requests.get(f"{self.ollama_host}/api/version")
            if response.status_code == 200:
                version = response.json().get("version", "unknown")
                logger.info(f"Connected to Ollama server (version {version})")
                return True
            else:
                logger.warning(f"Failed to connect to Ollama server: {response.status_code}")
                return False
        except Exception as e:
            logger.warning(f"Error connecting to Ollama server: {str(e)}")
            return False
    
    def transcribe_audio_file(self, audio_file, callback=None):
        """Transcribe audio file using Ollama"""
        thread = threading.Thread(
            target=self._transcribe_file,
            args=(audio_file, callback),
            daemon=True
        )
        thread.start()
    
    def _transcribe_file(self, audio_file, callback):
        """Transcribe audio file in a separate thread"""
        try:
            # Send to Ollama for transcription
            url = f"{self.ollama_host}/api/audio"
            
            with open(audio_file, 'rb') as f:
                files = {
                    'file': (os.path.basename(audio_file), f, 'audio/wav')
                }
                data = {
                    'model': self.model,
                    'format': 'json',
                    'language': 'en'
                }
                
                response = requests.post(url, files=files, data=data)
            
            if response.status_code == 200:
                result = response.json()
                text = result.get('text', '')
                if text and callback:
                    callback(text)
            else:
                logger.warning(f"Transcription failed: {response.status_code} - {response.text}")
                
        except Exception as e:
            logger.error(f"Error in transcription: {str(e)}")

class SummarizationService:
    """Service for summarizing text using Ollama"""
    
    def __init__(self, ollama_host, model="llama2"):
        self.ollama_host = ollama_host
        self.model = model
        self.test_connection()
    
    def test_connection(self):
        """Test connection to Ollama server"""
        try:
            response = requests.get(f"{self.ollama_host}/api/version")
            if response.status_code == 200:
                version = response.json().get("version", "unknown")
                logger.info(f"Connected to Ollama server (version {version})")
                return True
            else:
                logger.warning(f"Failed to connect to Ollama server: {response.status_code}")
                return False
        except Exception as e:
            logger.warning(f"Error connecting to Ollama server: {str(e)}")
            return False
    
    def summarize_text(self, text, prompt_template, callback=None):
        """Summarize text using Ollama"""
        thread = threading.Thread(
            target=self._generate_summary,
            args=(text, prompt_template, callback),
            daemon=True
        )
        thread.start()
    
    def _generate_summary(self, text, prompt_template, callback):
        """Generate summary using Ollama"""
        try:
            # Prepare prompt
            prompt = f"{prompt_template}\n\nText to summarize:\n{text}"
            
            # Send to Ollama
            url = f"{self.ollama_host}/api/generate"
            data = {
                "model": self.model,
                "prompt": prompt,
                "stream": False
            }
            
            response = requests.post(url, json=data)
            
            if response.status_code == 200:
                result = response.json()
                summary = result.get('response', '')
                if summary and callback:
                    callback(summary)
            else:
                logger.warning(f"Summarization failed: {response.status_code} - {response.text}")
                
        except Exception as e:
            logger.error(f"Error in summarization: {str(e)}")

class WindowsRecorderApp:
    """Application that uses Windows Sound Recorder for audio capture"""
    
    def __init__(self, root):
        self.root = root
        self.root.title("SumOnTheFly - Windows Recorder")
        self.root.geometry("800x600")
        self.root.minsize(600, 400)
        
        # Initialize components
        self.config = Config()
        
        # State variables
        self.is_recording = False
        self.recording_process = None
        self.current_recording_file = None
        self.recording_start_time = 0
        self.transcription_buffer = ""
        
        # Services
        self.transcription_service = TranscriptionService(
            ollama_host=self.config.get("ollama_host"),
            model=self.config.get("transcription_model")
        )
        
        self.summarization_service = SummarizationService(
            ollama_host=self.config.get("ollama_host"),
            model=self.config.get("summarization_model")
        )
        
        # Set up UI
        self.setup_ui()
        
        # Start UI update timer
        self.update_ui()
    
    def setup_ui(self):
        """Set up the user interface"""
        # Main frame
        main_frame = ttk.Frame(self.root, padding=10)
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # Create menu
        self.create_menu()
        
        # Control panel
        control_frame = ttk.LabelFrame(main_frame, text="Controls")
        control_frame.pack(fill=tk.X, pady=5)
        
        self.record_button = ttk.Button(
            control_frame, 
            text="Start Recording", 
            command=self.toggle_recording
        )
        self.record_button.pack(side=tk.LEFT, padx=5, pady=5)
        
        self.time_label = ttk.Label(control_frame, text="00:00:00")
        self.time_label.pack(side=tk.RIGHT, padx=5, pady=5)
        
        # Transcription panel
        transcription_frame = ttk.LabelFrame(main_frame, text="Real-time Transcription")
        transcription_frame.pack(fill=tk.BOTH, expand=True, pady=5)
        
        self.transcription_text = tk.Text(
            transcription_frame,
            wrap=tk.WORD,
            state=tk.DISABLED
        )
        transcription_scroll = ttk.Scrollbar(
            transcription_frame, 
            command=self.transcription_text.yview
        )
        self.transcription_text.configure(yscrollcommand=transcription_scroll.set)
        
        self.transcription_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        transcription_scroll.pack(side=tk.RIGHT, fill=tk.Y)
        
        # Summary panel
        summary_frame = ttk.LabelFrame(main_frame, text="Summaries")
        summary_frame.pack(fill=tk.BOTH, expand=True, pady=5)
        
        self.summary_text = tk.Text(
            summary_frame,
            wrap=tk.WORD,
            state=tk.DISABLED,
            height=10
        )
        summary_scroll = ttk.Scrollbar(
            summary_frame, 
            command=self.summary_text.yview
        )
        self.summary_text.configure(yscrollcommand=summary_scroll.set)
        
        self.summary_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        summary_scroll.pack(side=tk.RIGHT, fill=tk.Y)
        
        # Status bar
        self.status_bar = ttk.Label(
            self.root, 
            text="Ready", 
            relief=tk.SUNKEN, 
            anchor=tk.W
        )
        self.status_bar.pack(side=tk.BOTTOM, fill=tk.X)
    
    def create_menu(self):
        """Create the application menu"""
        menubar = tk.Menu(self.root)
        
        # File menu
        file_menu = tk.Menu(menubar, tearoff=0)
        file_menu.add_command(label="New Recording", command=self.new_recording)
        file_menu.add_separator()
        file_menu.add_command(label="Open Audio File", command=self.open_audio_file)
        file_menu.add_separator()
        file_menu.add_command(label="Exit", command=self.root.quit)
        menubar.add_cascade(label="File", menu=file_menu)
        
        # Settings menu
        settings_menu = tk.Menu(menubar, tearoff=0)
        settings_menu.add_command(label="Configure", command=self.show_settings)
        menubar.add_cascade(label="Settings", menu=settings_menu)
        
        # Help menu
        help_menu = tk.Menu(menubar, tearoff=0)
        help_menu.add_command(label="About", command=self.show_about)
        menubar.add_cascade(label="Help", menu=help_menu)
        
        self.root.config(menu=menubar)
    
    def update_ui(self):
        """Periodically update the UI"""
        if self.is_recording:
            # Update recording time
            elapsed = time.time() - self.recording_start_time
            hours, remainder = divmod(int(elapsed), 3600)
            minutes, seconds = divmod(remainder, 60)
            self.time_label.config(text=f"{hours:02d}:{minutes:02d}:{seconds:02d}")
        
        # Schedule the next update
        self.root.after(100, self.update_ui)
    
    def toggle_recording(self):
        """Start or stop recording"""
        if not self.is_recording:
            self.start_recording()
        else:
            self.stop_recording()
    
    def start_recording(self):
        """Start recording using Windows Sound Recorder"""
        try:
            # Generate a unique filename for this recording
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            self.current_recording_file = os.path.join(
                self.config.get("temp_dir"), 
                f"recording_{timestamp}.wav"
            )
            
            # Ensure the directory exists
            os.makedirs(os.path.dirname(self.current_recording_file), exist_ok=True)
            
            # Start Windows Sound Recorder via PowerShell
            # This uses the Windows built-in Sound Recorder (Windows.Media.Audio.SoundRecorder:)
            powershell_cmd = r"Start-Process 'powershell' -ArgumentList '-Command', \"Add-Type -AssemblyName PresentationCore,PresentationFramework,WindowsBase,System.Windows.Forms; [System.Windows.Forms.SendKeys]::SendWait('%{r}'); Start-Sleep -Seconds 1; [System.Windows.Forms.SendKeys]::SendWait(' ');\" -WindowStyle Hidden"
            
            self.recording_process = subprocess.Popen(["powershell", "-Command", powershell_cmd])
            
            # Update state and UI
            self.is_recording = True
            self.recording_start_time = time.time()
            self.record_button.config(text="Stop Recording")
            self.status_bar.config(text="Recording... (Windows Sound Recorder is running)")
            
            # Clear text areas
            self.transcription_text.config(state=tk.NORMAL)
            self.transcription_text.delete(1.0, tk.END)
            self.transcription_text.config(state=tk.DISABLED)
            
            self.summary_text.config(state=tk.NORMAL)
            self.summary_text.delete(1.0, tk.END)
            self.summary_text.config(state=tk.DISABLED)
            
            logger.info(f"Started recording with Windows Sound Recorder")
            
        except Exception as e:
            logger.error(f"Error starting recording: {str(e)}")
            messagebox.showerror("Error", f"Failed to start recording: {str(e)}")
    
    def stop_recording(self):
        """Stop recording and process the audio file"""
        if not self.is_recording:
            return
        
        try:
            # Stop the Windows Sound Recorder by sending keyboard shortcut
            # Alt+F4 to close the Sound Recorder window
            subprocess.run(["powershell", "-Command", "Add-Type -AssemblyName System.Windows.Forms; [System.Windows.Forms.SendKeys]::SendWait('%{F4}');"])
            
            # Wait for the save dialog to appear and save the file
            time.sleep(1)  # Give time for the save dialog to appear
            
            # Type the file path and press Enter
            save_cmd = f"Add-Type -AssemblyName System.Windows.Forms; [System.Windows.Forms.SendKeys]::SendWait('{self.current_recording_file}'); Start-Sleep -Seconds 1; [System.Windows.Forms.SendKeys]::SendWait('{{ENTER}}');"
            subprocess.run(["powershell", "-Command", save_cmd])
            
            # Update state and UI
            self.is_recording = False
            self.record_button.config(text="Start Recording")
            self.status_bar.config(text="Processing recording...")
            
            # Wait for the file to be saved
            time.sleep(2)
            
            # Process the audio file
            if os.path.exists(self.current_recording_file):
                self.process_audio_file(self.current_recording_file)
            else:
                # If the file doesn't exist, ask the user to locate it
                messagebox.showinfo("File Not Found", "Please locate the saved recording file.")
                audio_file = filedialog.askopenfilename(
                    title="Select the recording file",
                    filetypes=[("WAV files", "*.wav"), ("All files", "*.*")]
                )
                if audio_file:
                    self.process_audio_file(audio_file)
            
            logger.info("Stopped recording")
            
        except Exception as e:
            logger.error(f"Error stopping recording: {str(e)}")
            messagebox.showerror("Error", f"Error stopping recording: {str(e)}")
    
    def process_audio_file(self, audio_file):
        """Process the recorded audio file"""
        try:
            self.status_bar.config(text=f"Transcribing audio file: {os.path.basename(audio_file)}")
            
            # Transcribe the audio file
            self.transcription_service.transcribe_audio_file(
                audio_file,
                self.on_transcription
            )
            
        except Exception as e:
            logger.error(f"Error processing audio file: {str(e)}")
            self.status_bar.config(text=f"Error: {str(e)}")
    
    def on_transcription(self, text):
        """Handle transcription result"""
        if not text:
            return
        
        # Update the transcription buffer
        self.transcription_buffer += text + " "
        
        # Update the UI
        self.transcription_text.config(state=tk.NORMAL)
        self.transcription_text.insert(tk.END, text + " ")
        self.transcription_text.see(tk.END)
        self.transcription_text.config(state=tk.DISABLED)
        
        # Generate summary
        self.generate_summary()
        
        # Update status
        self.status_bar.config(text="Transcription complete. Generating summary...")
    
    def generate_summary(self):
        """Generate a summary from the transcription buffer"""
        if not self.summarization_service or not self.transcription_buffer.strip():
            return
        
        try:
            # Request summary asynchronously
            self.summarization_service.summarize_text(
                self.transcription_buffer,
                "Summarize the key points in 3-5 bullets.",
                self.on_summary
            )
            
        except Exception as e:
            logger.error(f"Error generating summary: {str(e)}")
            self.status_bar.config(text=f"Error generating summary: {str(e)}")
    
    def on_summary(self, summary):
        """Handle summary result"""
        if not summary:
            return
        
        # Format the summary with timestamp
        timestamp = datetime.now().strftime("%H:%M:%S")
        formatted_summary = f"\n[{timestamp}]\n{summary}\n"
        
        # Update the UI
        self.summary_text.config(state=tk.NORMAL)
        self.summary_text.insert(tk.END, formatted_summary)
        self.summary_text.see(tk.END)
        self.summary_text.config(state=tk.DISABLED)
        
        # Update status
        self.status_bar.config(text="Summary generated.")
    
    def new_recording(self):
        """Start a new recording"""
        if self.is_recording:
            if messagebox.askyesno("Confirm", "Stop current recording and start a new one?"):
                self.stop_recording()
                self.start_recording()
        else:
            self.start_recording()
    
    def open_audio_file(self):
        """Open an existing audio file for processing"""
        audio_file = filedialog.askopenfilename(
            title="Select Audio File",
            filetypes=[("WAV files", "*.wav"), ("All files", "*.*")]
        )
        
        if audio_file:
            # Clear previous data
            self.transcription_text.config(state=tk.NORMAL)
            self.transcription_text.delete(1.0, tk.END)
            self.transcription_text.config(state=tk.DISABLED)
            
            self.summary_text.config(state=tk.NORMAL)
            self.summary_text.delete(1.0, tk.END)
            self.summary_text.config(state=tk.DISABLED)
            
            self.transcription_buffer = ""
            
            # Process the audio file
            self.process_audio_file(audio_file)
    
    def show_settings(self):
        """Show the settings dialog"""
        # Create a new top-level window
        settings_window = tk.Toplevel(self.root)
        settings_window.title("Settings")
        settings_window.geometry("500x300")
        settings_window.transient(self.root)  # Make it modal
        settings_window.grab_set()
        
        # Create a notebook for tabbed settings
        notebook = ttk.Notebook(settings_window)
        notebook.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # Ollama settings tab
        ollama_frame = ttk.Frame(notebook, padding=10)
        notebook.add(ollama_frame, text="Ollama")
        
        ttk.Label(ollama_frame, text="Ollama Host:").grid(row=0, column=0, sticky=tk.W, pady=5)
        ollama_host = tk.StringVar(value=self.config.get("ollama_host", "http://localhost:11434"))
        ttk.Entry(ollama_frame, textvariable=ollama_host, width=40).grid(row=0, column=1, sticky=tk.W, pady=5)
        
        ttk.Label(ollama_frame, text="Transcription Model:").grid(row=1, column=0, sticky=tk.W, pady=5)
        transcription_model = tk.StringVar(value=self.config.get("transcription_model", "whisper"))
        ttk.Entry(ollama_frame, textvariable=transcription_model).grid(row=1, column=1, sticky=tk.W, pady=5)
        
        ttk.Label(ollama_frame, text="Summarization Model:").grid(row=2, column=0, sticky=tk.W, pady=5)
        summarization_model = tk.StringVar(value=self.config.get("summarization_model", "llama2"))
        ttk.Entry(ollama_frame, textvariable=summarization_model).grid(row=2, column=1, sticky=tk.W, pady=5)
        
        # Buttons
        def save_settings():
            try:
                # Update config
                self.config.set("ollama_host", ollama_host.get())
                self.config.set("transcription_model", transcription_model.get())
                self.config.set("summarization_model", summarization_model.get())
                
                # Save config
                self.config.save()
                
                # Reinitialize services
                self.transcription_service = TranscriptionService(
                    ollama_host=self.config.get("ollama_host"),
                    model=self.config.get("transcription_model")
                )
                
                self.summarization_service = SummarizationService(
                    ollama_host=self.config.get("ollama_host"),
                    model=self.config.get("summarization_model")
                )
                
                messagebox.showinfo("Success", "Settings saved successfully")
                settings_window.destroy()
            except Exception as e:
                messagebox.showerror("Error", f"Failed to save settings: {str(e)}")
        
        button_frame = ttk.Frame(settings_window)
        button_frame.pack(fill=tk.X, padx=10, pady=10)
        
        ttk.Button(button_frame, text="Save", command=save_settings).pack(side=tk.RIGHT, padx=5)
        ttk.Button(button_frame, text="Cancel", command=settings_window.destroy).pack(side=tk.RIGHT, padx=5)
    
    def show_about(self):
        """Show the about dialog"""
        about_text = """SumOnTheFly - Windows Recorder Edition v1.0
        
A desktop application that uses Windows Sound Recorder to capture audio, then transcribes it and generates bullet-point summaries.

Created with Python and Tkinter.
Uses Ollama for local AI processing.

Ollama Server: http://CarlsMacStudio.got.volvo.net:11434
Transcription Model: dimavz/whisper-tiny
Summarization Model: qwen3
        """
        messagebox.showinfo("About SumOnTheFly", about_text)

def main():
    """Main entry point for the application"""
    try:
        # Create the root window
        root = tk.Tk()
        
        # Create and run the application
        app = WindowsRecorderApp(root)
        
        # Handle window close
        def on_closing():
            if app.is_recording:
                if messagebox.askyesno("Confirm", "Recording is in progress. Stop recording and exit?"):
                    app.stop_recording()
                    root.destroy()
            else:
                root.destroy()
        
        root.protocol("WM_DELETE_WINDOW", on_closing)
        root.mainloop()
        
    except Exception as e:
        logger.critical(f"Unhandled exception: {str(e)}")
        messagebox.showerror("Critical Error", f"An unhandled error occurred: {str(e)}")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
