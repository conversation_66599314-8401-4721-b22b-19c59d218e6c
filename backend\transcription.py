import requests
import numpy as np
import logging
import threading
import time
import io
from typing import Optional, Callable, Dict, Any

logger = logging.getLogger(__name__)

# Try to import wave but don't fail if it's not available
try:
    import wave
    WAVE_AVAILABLE = True
except ImportError:
    logger.warning("wave module not available. Some audio processing features will be limited.")
    WAVE_AVAILABLE = False

class TranscriptionService:
    """
    Service for real-time audio transcription using Ollama's Whisper model.
    """
    
    def __init__(self, 
                 ollama_host: str = "http://localhost:11434",
                 model: str = "whisper",
                 on_transcription: Optional[Callable[[str], None]] = None,
                 language: str = "auto",
                 sample_rate: int = 16000):
        self.ollama_host = ollama_host
        self.model = model
        self.on_transcription = on_transcription
        self.language = language
        self.sample_rate = sample_rate
        self.is_processing = False
        self.audio_buffer = []
        self.buffer_lock = threading.Lock()
        self.processing_thread = None
        self.last_processed_time = 0
        self.min_process_interval = 2.0  # seconds
        self.test_mode = False
        
        # Test connection to Ollama server
        try:
            self._test_ollama_connection()
        except Exception as e:
            logger.warning(f"Could not connect to Ollama server at {ollama_host}: {str(e)}")
            logger.warning("Transcription service will run in test mode with simulated responses")
            self.test_mode = True
    
    def _test_ollama_connection(self):
        """Test connection to Ollama server"""
        try:
            response = requests.get(f"{self.ollama_host}/api/version", timeout=5)
            if response.status_code == 200:
                version = response.json().get("version", "unknown")
                logger.info(f"Connected to Ollama server (version {version})")
            else:
                raise Exception(f"Failed to connect: {response.status_code}")
        except Exception as e:
            logger.error(f"Error connecting to Ollama: {str(e)}")
            raise
    
    def process_audio(self, audio_chunk: np.ndarray) -> None:
        """Add audio chunk to buffer and process if needed"""
        with self.buffer_lock:
            self.audio_buffer.append(audio_chunk)
        
        # Check if we should start processing
        current_time = time.time()
        if (not self.is_processing and 
            current_time - self.last_processed_time >= self.min_process_interval):
            self._start_processing()
    
    def _start_processing(self) -> None:
        """Start processing the audio buffer in a separate thread"""
        if self.is_processing:
            return
        
        self.is_processing = True
        self.processing_thread = threading.Thread(
            target=self._process_buffer,
            daemon=True
        )
        self.processing_thread.start()
    
    def _process_buffer(self) -> None:
        """Process the accumulated audio buffer"""
        try:
            # Get a copy of the current buffer and clear it
            with self.buffer_lock:
                if not self.audio_buffer:
                    self.is_processing = False
                    return
                
                buffer_copy = self.audio_buffer.copy()
                self.audio_buffer = []
            
            # Combine audio chunks
            combined_audio = np.concatenate(buffer_copy)
            
            if self.test_mode:
                # Generate a simulated transcription in test mode
                transcription = self._simulate_transcription(len(combined_audio))
            else:
                # Convert to WAV format in memory
                wav_bytes = self._numpy_to_wav(combined_audio)
                
                # Send to Ollama for transcription
                transcription = self._transcribe_audio(wav_bytes)
            
            # Call the callback with the transcription result
            if transcription and self.on_transcription:
                self.on_transcription(transcription)
            
        except Exception as e:
            logger.error(f"Error processing audio buffer: {str(e)}")
        finally:
            self.last_processed_time = time.time()
            self.is_processing = False
            
            # If there's more data in the buffer, process it
            with self.buffer_lock:
                if self.audio_buffer:
                    self._start_processing()
    
    def _numpy_to_wav(self, audio_data: np.ndarray) -> bytes:
        """Convert numpy array to WAV format in memory"""
        if not WAVE_AVAILABLE:
            # If wave module is not available, return raw PCM data
            logger.warning("wave module not available, returning raw PCM data")
            return audio_data.tobytes()
            
        with io.BytesIO() as wav_buffer:
            with wave.open(wav_buffer, 'wb') as wf:
                wf.setnchannels(1)  # Mono
                wf.setsampwidth(2)  # 16-bit
                wf.setframerate(self.sample_rate)
                wf.writeframes(audio_data.tobytes())
            
            return wav_buffer.getvalue()
    
    def _transcribe_audio(self, audio_bytes: bytes) -> str:
        """Send audio to Ollama's Whisper model for transcription"""
        try:
            url = f"{self.ollama_host}/api/generate"
            
            # Prepare the request payload
            payload = {
                "model": self.model,
                "prompt": "Transcribe the following audio",
                "stream": False,
                "options": {
                    "audio": True,
                    "language": self.language if self.language != "auto" else None
                }
            }
            
            # Create multipart form data
            files = {
                'audio': ('audio.wav', audio_bytes, 'audio/wav')
            }
            
            # Send request to Ollama
            response = requests.post(url, json=payload, files=files, timeout=30)
            
            if response.status_code == 200:
                result = response.json()
                return result.get('response', '')
            else:
                logger.error(f"Transcription API error: {response.status_code} - {response.text}")
                return ""
                
        except Exception as e:
            logger.error(f"Error in transcription request: {str(e)}")
            return ""
    
    def _simulate_transcription(self, audio_length: int) -> str:
        """Generate a simulated transcription for testing"""
        # Calculate approximate duration in seconds
        duration = audio_length / self.sample_rate
        
        # Generate different test phrases based on time
        test_phrases = [
            "This is a test transcription.",
            "The application is running in test mode.",
            "No connection to Ollama server.",
            "Audio is being processed locally.",
            "This is simulated transcription data."
        ]
        
        # Select a phrase based on the current time
        phrase_index = int(time.time() / 5) % len(test_phrases)
        
        # Sleep to simulate processing time
        time.sleep(min(duration / 10, 0.5))  # Sleep for a fraction of the audio duration
        
        return test_phrases[phrase_index]
    
    def set_language(self, language: str) -> None:
        """Set the language for transcription"""
        self.language = language
    
    def set_model(self, model: str) -> None:
        """Set the Whisper model to use"""
        self.model = model


# Example usage
if __name__ == "__main__":
    logging.basicConfig(level=logging.INFO)
    
    def on_transcription_result(text):
        print(f"Transcription: {text}")
    
    # Create transcription service
    service = TranscriptionService(
        on_transcription=on_transcription_result
    )
    
    # Generate some test audio (sine wave)
    sample_rate = 16000
    duration = 3  # seconds
    t = np.linspace(0, duration, int(sample_rate * duration), False)
    audio = np.sin(2 * np.pi * 440 * t) * 32767  # 440 Hz tone
    audio = audio.astype(np.int16)
    
    # Process the audio
    service.process_audio(audio)
    
    # Wait for processing to complete
    time.sleep(5)
