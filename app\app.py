#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import tkinter as tk
import logging
import os
import sys
from main import SumOnTheFlyApp

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("sumonthefly.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

def main():
    """Main entry point for the application"""
    try:
        # Create the root window
        root = tk.Tk()
        root.title("SumOnTheFly")
        
        # Set application icon
        # if os.path.exists("assets/icon.ico"):
        #     root.iconbitmap("assets/icon.ico")
        
        # Create and run the application
        app = SumOnTheFlyApp(root)
        root.mainloop()
        
    except Exception as e:
        logger.critical(f"Unhandled exception: {str(e)}")
        return 1
    
    return 0


if __name__ == "__main__":
    sys.exit(main())
