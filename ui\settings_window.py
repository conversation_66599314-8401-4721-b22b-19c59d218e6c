import tkinter as tk
from tkinter import ttk, messagebox
import logging
import json
import os
import requests

logger = logging.getLogger(__name__)

class SettingsWindow:
    """Settings window for configuring application parameters"""
    
    def __init__(self, parent, config):
        self.parent = parent
        self.config = config
        
        # Create a new top-level window
        self.window = tk.Toplevel(parent)
        self.window.title("Settings")
        self.window.geometry("500x600")
        self.window.minsize(400, 500)
        self.window.transient(parent)  # Set as a child of the parent window
        self.window.grab_set()  # Make this window modal
        
        # Center the window relative to the parent
        self.center_window()
        
        # Create the UI
        self.create_ui()
        
        # Load current settings
        self.load_settings()
    
    def center_window(self):
        """Center the window on the screen"""
        self.window.update_idletasks()
        
        # Get parent window position and size
        parent_x = self.parent.winfo_x()
        parent_y = self.parent.winfo_y()
        parent_width = self.parent.winfo_width()
        parent_height = self.parent.winfo_height()
        
        # Get this window's size
        width = self.window.winfo_width()
        height = self.window.winfo_height()
        
        # Calculate position
        x = parent_x + (parent_width - width) // 2
        y = parent_y + (parent_height - height) // 2
        
        # Set position
        self.window.geometry(f"+{x}+{y}")
    
    def create_ui(self):
        """Create the settings UI"""
        # Main frame with padding
        main_frame = ttk.Frame(self.window, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # Create notebook (tabbed interface)
        notebook = ttk.Notebook(main_frame)
        notebook.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # Create tabs
        general_tab = ttk.Frame(notebook)
        ollama_tab = ttk.Frame(notebook)
        audio_tab = ttk.Frame(notebook)
        
        notebook.add(general_tab, text="General")
        notebook.add(ollama_tab, text="Ollama")
        notebook.add(audio_tab, text="Audio")
        
        # Populate tabs
        self.create_general_settings(general_tab)
        self.create_ollama_settings(ollama_tab)
        self.create_audio_settings(audio_tab)
        
        # Buttons frame
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X, pady=10)
        
        # Save and Cancel buttons
        save_button = ttk.Button(
            button_frame, 
            text="Save", 
            command=self.save_settings
        )
        save_button.pack(side=tk.RIGHT, padx=5)
        
        cancel_button = ttk.Button(
            button_frame, 
            text="Cancel", 
            command=self.window.destroy
        )
        cancel_button.pack(side=tk.RIGHT, padx=5)
        
        # Test connection button for Ollama
        test_button = ttk.Button(
            button_frame, 
            text="Test Ollama Connection", 
            command=self.test_ollama_connection
        )
        test_button.pack(side=tk.LEFT, padx=5)
    
    def create_general_settings(self, parent):
        """Create general settings controls"""
        # Create a frame with padding
        frame = ttk.Frame(parent, padding="10")
        frame.pack(fill=tk.BOTH, expand=True)
        
        # Summary interval setting
        ttk.Label(frame, text="Summary Interval (seconds):").grid(
            row=0, column=0, sticky=tk.W, pady=5
        )
        
        self.summary_interval_var = tk.StringVar()
        summary_interval_entry = ttk.Entry(
            frame, 
            textvariable=self.summary_interval_var,
            width=10
        )
        summary_interval_entry.grid(row=0, column=1, sticky=tk.W, pady=5)
        
        # Language setting
        ttk.Label(frame, text="Language:").grid(
            row=1, column=0, sticky=tk.W, pady=5
        )
        
        self.language_var = tk.StringVar()
        languages = ["English", "Swedish", "Spanish", "French", "German", "Auto-detect"]
        language_combo = ttk.Combobox(
            frame, 
            textvariable=self.language_var,
            values=languages,
            state="readonly",
            width=20
        )
        language_combo.grid(row=1, column=1, sticky=tk.W, pady=5)
        
        # Auto-start recording option
        self.auto_start_var = tk.BooleanVar()
        auto_start_check = ttk.Checkbutton(
            frame, 
            text="Auto-start recording on application launch", 
            variable=self.auto_start_var
        )
        auto_start_check.grid(row=2, column=0, columnspan=2, sticky=tk.W, pady=5)
        
        # Data directory setting
        ttk.Label(frame, text="Data Directory:").grid(
            row=3, column=0, sticky=tk.W, pady=5
        )
        
        data_dir_frame = ttk.Frame(frame)
        data_dir_frame.grid(row=3, column=1, sticky=tk.W, pady=5)
        
        self.data_dir_var = tk.StringVar()
        data_dir_entry = ttk.Entry(
            data_dir_frame, 
            textvariable=self.data_dir_var,
            width=30
        )
        data_dir_entry.pack(side=tk.LEFT)
        
        browse_button = ttk.Button(
            data_dir_frame, 
            text="Browse", 
            command=self.browse_data_dir
        )
        browse_button.pack(side=tk.LEFT, padx=5)
    
    def create_ollama_settings(self, parent):
        """Create Ollama settings controls"""
        # Create a frame with padding
        frame = ttk.Frame(parent, padding="10")
        frame.pack(fill=tk.BOTH, expand=True)
        
        # Ollama host setting
        ttk.Label(frame, text="Ollama Host:").grid(
            row=0, column=0, sticky=tk.W, pady=5
        )
        
        self.ollama_host_var = tk.StringVar()
        ollama_host_entry = ttk.Entry(
            frame, 
            textvariable=self.ollama_host_var,
            width=30
        )
        ollama_host_entry.grid(row=0, column=1, sticky=tk.W, pady=5)
        
        # Transcription model setting
        ttk.Label(frame, text="Transcription Model:").grid(
            row=1, column=0, sticky=tk.W, pady=5
        )
        
        self.transcription_model_var = tk.StringVar()
        transcription_models = ["whisper", "whisper-small", "whisper-medium", "whisper-large"]
        transcription_model_combo = ttk.Combobox(
            frame, 
            textvariable=self.transcription_model_var,
            values=transcription_models,
            state="readonly",
            width=20
        )
        transcription_model_combo.grid(row=1, column=1, sticky=tk.W, pady=5)
        
        # Summarization model setting
        ttk.Label(frame, text="Summarization Model:").grid(
            row=2, column=0, sticky=tk.W, pady=5
        )
        
        self.summarization_model_var = tk.StringVar()
        summarization_models = ["llama2", "llama2:13b", "mistral", "mixtral", "gemma:7b"]
        summarization_model_combo = ttk.Combobox(
            frame, 
            textvariable=self.summarization_model_var,
            values=summarization_models,
            state="readonly",
            width=20
        )
        summarization_model_combo.grid(row=2, column=1, sticky=tk.W, pady=5)
        
        # Model parameters
        ttk.Label(frame, text="Model Parameters:").grid(
            row=3, column=0, sticky=tk.W, pady=5
        )
        
        self.model_params_var = tk.StringVar()
        model_params_entry = ttk.Entry(
            frame, 
            textvariable=self.model_params_var,
            width=30
        )
        model_params_entry.grid(row=3, column=1, sticky=tk.W, pady=5)
        ttk.Label(frame, text="Format: {\"temperature\": 0.7, \"top_p\": 0.9}").grid(
            row=4, column=1, sticky=tk.W
        )
        
        # Refresh models button
        refresh_button = ttk.Button(
            frame, 
            text="Refresh Model List", 
            command=self.refresh_model_list
        )
        refresh_button.grid(row=5, column=0, columnspan=2, pady=10)
    
    def create_audio_settings(self, parent):
        """Create audio settings controls"""
        # Create a frame with padding
        frame = ttk.Frame(parent, padding="10")
        frame.pack(fill=tk.BOTH, expand=True)
        
        # Sample rate setting
        ttk.Label(frame, text="Sample Rate (Hz):").grid(
            row=0, column=0, sticky=tk.W, pady=5
        )
        
        self.sample_rate_var = tk.StringVar()
        sample_rates = ["16000", "22050", "44100", "48000"]
        sample_rate_combo = ttk.Combobox(
            frame, 
            textvariable=self.sample_rate_var,
            values=sample_rates,
            state="readonly",
            width=10
        )
        sample_rate_combo.grid(row=0, column=1, sticky=tk.W, pady=5)
        
        # Channels setting
        ttk.Label(frame, text="Channels:").grid(
            row=1, column=0, sticky=tk.W, pady=5
        )
        
        self.channels_var = tk.StringVar()
        channels_combo = ttk.Combobox(
            frame, 
            textvariable=self.channels_var,
            values=["1 (Mono)", "2 (Stereo)"],
            state="readonly",
            width=10
        )
        channels_combo.grid(row=1, column=1, sticky=tk.W, pady=5)
        
        # Chunk size setting
        ttk.Label(frame, text="Chunk Size:").grid(
            row=2, column=0, sticky=tk.W, pady=5
        )
        
        self.chunk_size_var = tk.StringVar()
        chunk_sizes = ["512", "1024", "2048", "4096"]
        chunk_size_combo = ttk.Combobox(
            frame, 
            textvariable=self.chunk_size_var,
            values=chunk_sizes,
            state="readonly",
            width=10
        )
        chunk_size_combo.grid(row=2, column=1, sticky=tk.W, pady=5)
        
        # Audio device selection
        ttk.Label(frame, text="Input Device:").grid(
            row=3, column=0, sticky=tk.W, pady=5
        )
        
        self.input_device_var = tk.StringVar()
        input_device_combo = ttk.Combobox(
            frame, 
            textvariable=self.input_device_var,
            state="readonly",
            width=30
        )
        input_device_combo.grid(row=3, column=1, sticky=tk.W, pady=5)
        
        # Populate audio devices
        refresh_devices_button = ttk.Button(
            frame, 
            text="Refresh Devices", 
            command=lambda: self.populate_audio_devices(input_device_combo)
        )
        refresh_devices_button.grid(row=4, column=0, columnspan=2, pady=10)
        
        # Initial population of devices
        self.populate_audio_devices(input_device_combo)
    
    def populate_audio_devices(self, combo_box):
        """Populate the audio devices dropdown"""
        try:
            import pyaudio
            p = pyaudio.PyAudio()
            
            devices = []
            for i in range(p.get_device_count()):
                device_info = p.get_device_info_by_index(i)
                if device_info['maxInputChannels'] > 0:  # Input device
                    device_name = f"{i}: {device_info['name']}"
                    devices.append(device_name)
            
            p.terminate()
            
            if devices:
                combo_box['values'] = devices
                if not self.input_device_var.get() and devices:
                    self.input_device_var.set(devices[0])
            else:
                combo_box['values'] = ["No input devices found"]
                self.input_device_var.set("No input devices found")
                
        except Exception as e:
            logger.error(f"Error populating audio devices: {str(e)}")
            combo_box['values'] = ["Error loading devices"]
            self.input_device_var.set("Error loading devices")
    
    def load_settings(self):
        """Load current settings into the UI"""
        # General settings
        self.summary_interval_var.set(str(self.config.get("summary_interval", 30)))
        self.language_var.set(self.config.get("language", "English"))
        self.auto_start_var.set(self.config.get("auto_start", False))
        self.data_dir_var.set(self.config.get("data_dir", os.path.join(os.path.expanduser("~"), "SumOnTheFly")))
        
        # Ollama settings
        self.ollama_host_var.set(self.config.get("ollama_host", "http://localhost:11434"))
        self.transcription_model_var.set(self.config.get("transcription_model", "whisper"))
        self.summarization_model_var.set(self.config.get("summarization_model", "llama2"))
        
        # Model parameters as JSON string
        model_params = self.config.get("model_params", {"temperature": 0.7, "top_p": 0.9})
        self.model_params_var.set(json.dumps(model_params))
        
        # Audio settings
        self.sample_rate_var.set(str(self.config.get("sample_rate", 16000)))
        channels = self.config.get("channels", 1)
        self.channels_var.set(f"{channels} ({'Mono' if channels == 1 else 'Stereo'})")
        self.chunk_size_var.set(str(self.config.get("chunk_size", 1024)))
        
        # Input device will be set by populate_audio_devices
        device_index = self.config.get("input_device_index", None)
        if device_index is not None:
            self.input_device_var.set(f"{device_index}: {self.config.get('input_device_name', '')}")
    
    def save_settings(self):
        """Save settings from the UI to the configuration"""
        try:
            # General settings
            self.config.set("summary_interval", int(self.summary_interval_var.get()))
            self.config.set("language", self.language_var.get())
            self.config.set("auto_start", self.auto_start_var.get())
            self.config.set("data_dir", self.data_dir_var.get())
            
            # Ollama settings
            self.config.set("ollama_host", self.ollama_host_var.get())
            self.config.set("transcription_model", self.transcription_model_var.get())
            self.config.set("summarization_model", self.summarization_model_var.get())
            
            # Parse model parameters JSON
            try:
                model_params = json.loads(self.model_params_var.get())
                self.config.set("model_params", model_params)
            except json.JSONDecodeError:
                messagebox.showerror("Error", "Invalid JSON format for model parameters")
                return
            
            # Audio settings
            self.config.set("sample_rate", int(self.sample_rate_var.get()))
            
            # Parse channels from UI string (e.g., "1 (Mono)")
            channels_str = self.channels_var.get()
            channels = 1 if "1" in channels_str else 2
            self.config.set("channels", channels)
            
            self.config.set("chunk_size", int(self.chunk_size_var.get()))
            
            # Parse device index from UI string (e.g., "0: Default")
            device_str = self.input_device_var.get()
            if device_str and ":" in device_str:
                device_index = int(device_str.split(":")[0].strip())
                device_name = device_str.split(":", 1)[1].strip()
                self.config.set("input_device_index", device_index)
                self.config.set("input_device_name", device_name)
            
            # Save configuration
            self.config.save()
            
            messagebox.showinfo("Success", "Settings saved successfully")
            self.window.destroy()
            
        except Exception as e:
            logger.error(f"Error saving settings: {str(e)}")
            messagebox.showerror("Error", f"Failed to save settings: {str(e)}")
    
    def browse_data_dir(self):
        """Open a directory browser dialog"""
        from tkinter import filedialog
        directory = filedialog.askdirectory(
            initialdir=self.data_dir_var.get(),
            title="Select Data Directory"
        )
        if directory:
            self.data_dir_var.set(directory)
    
    def refresh_model_list(self):
        """Refresh the list of available Ollama models"""
        try:
            host = self.ollama_host_var.get()
            response = requests.get(f"{host}/api/tags")
            
            if response.status_code == 200:
                models = response.json().get("models", [])
                model_names = [model["name"] for model in models]
                
                # Update model dropdowns
                transcription_models = [m for m in model_names if "whisper" in m.lower()]
                if transcription_models:
                    transcription_combo = self.window.nametowidget(".!toplevel.!frame.!notebook.!frame2.!combobox")
                    transcription_combo["values"] = transcription_models
                
                # Filter for LLM models (non-whisper models)
                llm_models = [m for m in model_names if "whisper" not in m.lower()]
                if llm_models:
                    summarization_combo = self.window.nametowidget(".!toplevel.!frame.!notebook.!frame2.!combobox2")
                    summarization_combo["values"] = llm_models
                
                messagebox.showinfo("Success", f"Found {len(model_names)} models")
            else:
                messagebox.showerror("Error", f"Failed to retrieve models: {response.status_code}")
                
        except Exception as e:
            logger.error(f"Error refreshing model list: {str(e)}")
            messagebox.showerror("Error", f"Failed to refresh model list: {str(e)}")
    
    def test_ollama_connection(self):
        """Test the connection to the Ollama server"""
        try:
            host = self.ollama_host_var.get()
            response = requests.get(f"{host}/api/version")
            
            if response.status_code == 200:
                version = response.json().get("version", "unknown")
                messagebox.showinfo("Success", f"Connected to Ollama server (version {version})")
            else:
                messagebox.showerror("Error", f"Failed to connect: {response.status_code}")
                
        except Exception as e:
            logger.error(f"Error testing Ollama connection: {str(e)}")
            messagebox.showerror("Error", f"Failed to connect to Ollama: {str(e)}")
