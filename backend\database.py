import sqlite3
import os
import json
import logging
from datetime import datetime
from typing import Dict, List, Any, Optional, Tuple

logger = logging.getLogger(__name__)

class DatabaseManager:
    """
    Manages database operations for storing session data, transcriptions, and summaries.
    """
    
    def __init__(self, db_path: Optional[str] = None):
        # Default database location
        if db_path is None:
            data_dir = os.path.join(os.path.expanduser("~"), "SumOnTheFly")
            os.makedirs(data_dir, exist_ok=True)
            db_path = os.path.join(data_dir, "sumonthefly.db")
        
        self.db_path = db_path
        self._initialize_db()
    
    def _initialize_db(self) -> None:
        """Initialize the database schema if it doesn't exist"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # Create sessions table
            cursor.execute('''
            CREATE TABLE IF NOT EXISTS sessions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                start_time REAL NOT NULL,
                end_time REAL,
                metadata TEXT
            )
            ''')
            
            # Create transcriptions table
            cursor.execute('''
            CREATE TABLE IF NOT EXISTS transcriptions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                session_id INTEGER NOT NULL,
                text TEXT NOT NULL,
                timestamp REAL NOT NULL,
                FOREIGN KEY (session_id) REFERENCES sessions (id) ON DELETE CASCADE
            )
            ''')
            
            # Create summaries table
            cursor.execute('''
            CREATE TABLE IF NOT EXISTS summaries (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                session_id INTEGER NOT NULL,
                text TEXT NOT NULL,
                timestamp REAL NOT NULL,
                FOREIGN KEY (session_id) REFERENCES sessions (id) ON DELETE CASCADE
            )
            ''')
            
            # Enable foreign key support
            cursor.execute("PRAGMA foreign_keys = ON")
            
            conn.commit()
            conn.close()
            
            logger.info(f"Database initialized at {self.db_path}")
            
        except Exception as e:
            logger.error(f"Error initializing database: {str(e)}")
            raise
    
    def create_session(self, name: str, metadata: Dict[str, Any] = None) -> int:
        """Create a new recording session"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            start_time = datetime.now().timestamp()
            metadata_json = json.dumps(metadata) if metadata else None
            
            cursor.execute(
                "INSERT INTO sessions (name, start_time, metadata) VALUES (?, ?, ?)",
                (name, start_time, metadata_json)
            )
            
            session_id = cursor.lastrowid
            conn.commit()
            conn.close()
            
            logger.info(f"Created new session: {name} (ID: {session_id})")
            return session_id
            
        except Exception as e:
            logger.error(f"Error creating session: {str(e)}")
            raise
    
    def update_session_end_time(self, session_id: int) -> bool:
        """Update the end time of a session"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            end_time = datetime.now().timestamp()
            
            cursor.execute(
                "UPDATE sessions SET end_time = ? WHERE id = ?",
                (end_time, session_id)
            )
            
            conn.commit()
            conn.close()
            
            logger.info(f"Updated end time for session ID: {session_id}")
            return True
            
        except Exception as e:
            logger.error(f"Error updating session end time: {str(e)}")
            return False
    
    def add_transcription(self, session_id: int, text: str, timestamp: float) -> int:
        """Add a transcription to a session"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute(
                "INSERT INTO transcriptions (session_id, text, timestamp) VALUES (?, ?, ?)",
                (session_id, text, timestamp)
            )
            
            transcription_id = cursor.lastrowid
            conn.commit()
            conn.close()
            
            return transcription_id
            
        except Exception as e:
            logger.error(f"Error adding transcription: {str(e)}")
            return -1
    
    def add_summary(self, session_id: int, text: str, timestamp: float) -> int:
        """Add a summary to a session"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute(
                "INSERT INTO summaries (session_id, text, timestamp) VALUES (?, ?, ?)",
                (session_id, text, timestamp)
            )
            
            summary_id = cursor.lastrowid
            conn.commit()
            conn.close()
            
            return summary_id
            
        except Exception as e:
            logger.error(f"Error adding summary: {str(e)}")
            return -1
    
    def get_session_data(self, session_id: int) -> Dict[str, Any]:
        """Get all data for a session including transcriptions and summaries"""
        try:
            conn = sqlite3.connect(self.db_path)
            conn.row_factory = sqlite3.Row  # Return rows as dictionaries
            cursor = conn.cursor()
            
            # Get session info
            cursor.execute("SELECT * FROM sessions WHERE id = ?", (session_id,))
            session = dict(cursor.fetchone())
            
            # Get transcriptions
            cursor.execute(
                "SELECT * FROM transcriptions WHERE session_id = ? ORDER BY timestamp",
                (session_id,)
            )
            transcriptions = [dict(row) for row in cursor.fetchall()]
            
            # Get summaries
            cursor.execute(
                "SELECT * FROM summaries WHERE session_id = ? ORDER BY timestamp",
                (session_id,)
            )
            summaries = [dict(row) for row in cursor.fetchall()]
            
            conn.close()
            
            # Parse metadata
            if session.get('metadata'):
                session['metadata'] = json.loads(session['metadata'])
            
            # Combine all data
            return {
                "session": session,
                "transcriptions": transcriptions,
                "summaries": summaries
            }
            
        except Exception as e:
            logger.error(f"Error getting session data: {str(e)}")
            return {}
    
    def get_all_sessions(self) -> List[Dict[str, Any]]:
        """Get a list of all sessions"""
        try:
            conn = sqlite3.connect(self.db_path)
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()
            
            cursor.execute("SELECT * FROM sessions ORDER BY start_time DESC")
            sessions = [dict(row) for row in cursor.fetchall()]
            
            conn.close()
            
            # Parse metadata for each session
            for session in sessions:
                if session.get('metadata'):
                    session['metadata'] = json.loads(session['metadata'])
            
            return sessions
            
        except Exception as e:
            logger.error(f"Error getting all sessions: {str(e)}")
            return []
    
    def delete_session(self, session_id: int) -> bool:
        """Delete a session and all its associated data"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # Enable foreign key support to cascade delete
            cursor.execute("PRAGMA foreign_keys = ON")
            
            cursor.execute("DELETE FROM sessions WHERE id = ?", (session_id,))
            
            conn.commit()
            conn.close()
            
            logger.info(f"Deleted session ID: {session_id}")
            return True
            
        except Exception as e:
            logger.error(f"Error deleting session: {str(e)}")
            return False


# Example usage
if __name__ == "__main__":
    logging.basicConfig(level=logging.INFO)
    
    # Create database manager
    db = DatabaseManager()
    
    # Create a test session
    session_id = db.create_session("Test Session", {"test": True})
    
    # Add some transcriptions
    db.add_transcription(session_id, "This is a test transcription.", datetime.now().timestamp())
    db.add_transcription(session_id, "This is another test transcription.", datetime.now().timestamp())
    
    # Add a summary
    db.add_summary(session_id, "- Test transcription\n- Another test", datetime.now().timestamp())
    
    # Get session data
    session_data = db.get_session_data(session_id)
    print(f"Session: {session_data['session']['name']}")
    print(f"Transcriptions: {len(session_data['transcriptions'])}")
    print(f"Summaries: {len(session_data['summaries'])}")
    
    # Update session end time
    db.update_session_end_time(session_id)
