#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import shutil
import sys
import glob

def cleanup_project():
    """Clean up the project directory by organizing files"""
    print("Cleaning up SumOnTheFly project...")
    
    # Create directories if they don't exist
    directories = ['app', 'utils', 'archive', 'tests', 'logs']
    for directory in directories:
        os.makedirs(directory, exist_ok=True)
        print(f"Created directory: {directory}")
    
    # Essential files to keep in the root directory
    essential_files = [
        'README.md',
        'requirements.txt',
        'whisper_transcriber.py',  # Our new main application
        '.env',
        '.env.example',
        'recording_20250430_184728.wav',  # Test audio file
        '.gitignore',
        'cleanup.py',
    ]
    
    # Files to move to app directory
    app_files = [
        'app.py',
        'main.py',
        'config.py',
        'config.json',
    ]
    
    # Files to move to utils directory
    utils_files = [
        'build_audio_exe.py',
    ]
    
    # Files to move to tests directory
    test_files = [
        'test_audio.py',
        'test_audio_level.py',
        'test_ollama_connection.py',
        'test_pyaudio.wav',
        'test_recording.wav',
        'test_sounddevice.wav',
        'test_synthetic.wav',
        'test_recorder.py',
    ]
    
    # Log files to move to logs directory
    log_patterns = ['*.log']
    
    # Files to archive (move to archive directory)
    archive_files = [
        'audio_app.py',
        'basic_recorder.py',
        'build_audio_recorder.py',
        'build_exe.py',
        'build_final_exe.py',
        'build_full_exe.py',
        'build_improved_exe.py',
        'build_simple_exe.py',
        'build_simplified_exe.py',
        'build_standalone_exe.py',
        'callback_recorder.py',
        'check_models.py',
        'direct_recorder.py',
        'ell_transcriber.py',
        'file_only_transcriber.py',
        'file_transcriber.py',
        'final_app.py',
        'full_app.py',
        'offline_simulator.py',
        'offline_transcriber.py',
        'openai_transcriber.py',
        'openai_transcriber_simple.py',
        'powershell_recorder.py',
        'robust_recorder.py',
        'run_full_app.bat',
        'run_improved_app.bat',
        'run_simple_app.bat',
        'simple_app.py',
        'simple_audio_test.py',
        'simple_recorder.py',
        'simple_transcriber.py',
        'simplified_app.py',
        'standalone_app.py',
        'transcription_recorder.py',
        'visual_recorder.py',
        'windows_recorder.py',
        'final_config.json',
        'Testing.py',
        '*.spec',
    ]
    
    # Process log files first
    for pattern in log_patterns:
        for log_file in glob.glob(pattern):
            if os.path.isfile(log_file):
                try:
                    shutil.move(log_file, os.path.join('logs', log_file))
                    print(f"Moved log file {log_file} to logs directory")
                except Exception as e:
                    print(f"Error moving log file {log_file}: {e}")
    
    # Get all files in the current directory
    all_files = [f for f in os.listdir('.') if os.path.isfile(f)]
    
    # Move files to appropriate directories
    for file in all_files:
        # Skip essential files
        if file in essential_files:
            continue
        
        # Move app files
        if file in app_files:
            try:
                shutil.move(file, os.path.join('app', file))
                print(f"Moved {file} to app directory")
            except Exception as e:
                print(f"Error moving {file} to app directory: {e}")
        
        # Move utils files
        elif file in utils_files:
            try:
                shutil.move(file, os.path.join('utils', file))
                print(f"Moved {file} to utils directory")
            except Exception as e:
                print(f"Error moving {file} to utils directory: {e}")
        
        # Move test files
        elif file in test_files:
            try:
                shutil.move(file, os.path.join('tests', file))
                print(f"Moved {file} to tests directory")
            except Exception as e:
                print(f"Error moving {file} to tests directory: {e}")
        
        # Archive other files
        else:
            should_archive = False
            
            # Check if file matches any archive pattern
            for pattern in archive_files:
                if '*' in pattern:
                    if glob.fnmatch.fnmatch(file, pattern):
                        should_archive = True
                        break
                elif file == pattern:
                    should_archive = True
                    break
            
            if should_archive:
                try:
                    shutil.move(file, os.path.join('archive', file))
                    print(f"Archived {file}")
                except Exception as e:
                    print(f"Error archiving {file}: {e}")
    
    print("\nProject cleanup complete!")
    print("\nEssential files in root directory:")
    for file in [f for f in os.listdir('.') if os.path.isfile(f)]:
        print(f"  - {file}")

def main():
    """Main entry point"""
    # Ask for confirmation
    print("This script will organize the SumOnTheFly project files.")
    print("It will create directories (app, utils, archive, tests, logs) and move files accordingly.")
    print("\nThe following files will remain in the root directory:")
    print("  - README.md")
    print("  - requirements.txt")
    print("  - whisper_transcriber.py (main application)")
    print("  - .env and .env.example")
    print("  - recording_20250430_184728.wav (test audio file)")
    print("  - cleanup.py (this script)")
    print("  - .gitignore")
    
    response = input("\nDo you want to proceed? (y/n): ")
    if response.lower() != 'y':
        print("Cleanup cancelled.")
        return 1
    
    cleanup_project()
    return 0

if __name__ == "__main__":
    sys.exit(main())
