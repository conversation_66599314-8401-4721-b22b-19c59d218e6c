# SumOnTheFly

A desktop application that records audio (microphone + system audio), transcribes it in real-time using Ollama's Whisper model, and generates bullet-point summaries every 30 seconds using a local LLM through Ollama.

## Features

- Cross-platform audio capture (microphone + system audio)
- Real-time transcription using Ollama's Whisper model
- Automatic summarization every 30 seconds using local LLM
- Python Tkinter UI with real-time view
- Export to PDF/Word reports
- Configurable settings (model selection, summary interval, language)

## Architecture

- Frontend: Python with Tkinter
- Backend: Python for audio processing, transcription, and summarization
- AI Processing: Ollama for local LLM inference
- Data storage: SQLite
- Deployment: Packaged as executable for Windows and macOS

## Installation

### Prerequisites

- Python 3.9+
- Ollama installed and running locally
- Appropriate Ollama models downloaded (whisper and a text generation model)

### Setup

1. Clone the repository
2. Install Python dependencies:
   ```
   pip install -r requirements.txt
   ```
3. Ensure Ollama is running with the required models

## Usage

1. Start the application:
   ```
   python main.py
   ```
2. Configure your Ollama settings in the configuration panel
3. Click "Start Recording" to begin capturing audio
4. View real-time transcriptions and summaries in the main interface
5. Export your session as PDF/Word when finished

### Simplified Version

A simplified version that works without external dependencies is also available:

```
python simple_app.py
```

This version uses simulated audio and AI responses, making it perfect for demonstrations or when Ollama is not available. It provides the same user interface but doesn't require audio capture devices or connection to an Ollama server.

## Development

### Running tests

```
python -m pytest tests/
```

### Building for distribution

```
python -m PyInstaller main.spec  # Full version
python build_simple_exe.py       # Simplified version
```

## License

MIT
