#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import threading
import time
import numpy as np
import logging
import sys
import os
from datetime import datetime

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("recorder.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# Check for PyAudio
try:
    import pyaudio
    import wave
    AUDIO_AVAILABLE = True
    logger.info("PyAudio and wave modules are available")
except ImportError as e:
    AUDIO_AVAILABLE = False
    logger.error(f"Audio modules not available: {str(e)}")

class SimpleRecorder:
    """Simple audio recorder with visualization"""
    
    def __init__(self, root):
        self.root = root
        self.root.title("Simple Audio Recorder")
        self.root.geometry("600x400")
        self.root.minsize(500, 300)
        
        # Audio parameters
        self.sample_rate = 16000
        self.channels = 1
        self.chunk_size = 1024
        self.format = pyaudio.paInt16 if AUDIO_AVAILABLE else None
        
        # State variables
        self.is_recording = False
        self.audio_data = []
        self.selected_device_index = None
        self.audio_capture = None
        self.stream = None
        self.recording_thread = None
        self.audio_level = 0
        
        # Set up UI
        self.setup_ui()
        
        # Detect audio devices
        self.detect_audio_devices()
        
        # Start UI update timer
        self.update_ui()
    
    def setup_ui(self):
        """Set up the user interface"""
        # Main frame
        main_frame = ttk.Frame(self.root, padding=10)
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # Device selection
        device_frame = ttk.LabelFrame(main_frame, text="Audio Device")
        device_frame.pack(fill=tk.X, pady=5)
        
        self.device_var = tk.StringVar()
        self.device_dropdown = ttk.Combobox(device_frame, textvariable=self.device_var, state="readonly")
        self.device_dropdown.pack(side=tk.LEFT, padx=5, pady=5, fill=tk.X, expand=True)
        self.device_dropdown.bind("<<ComboboxSelected>>", self.on_device_selected)
        
        refresh_button = ttk.Button(device_frame, text="Refresh", command=self.detect_audio_devices)
        refresh_button.pack(side=tk.RIGHT, padx=5, pady=5)
        
        # Control panel
        control_frame = ttk.LabelFrame(main_frame, text="Controls")
        control_frame.pack(fill=tk.X, pady=5)
        
        self.record_button = ttk.Button(
            control_frame, 
            text="Start Recording", 
            command=self.toggle_recording
        )
        self.record_button.pack(side=tk.LEFT, padx=5, pady=5)
        
        self.save_button = ttk.Button(
            control_frame, 
            text="Save Recording", 
            command=self.save_recording,
            state=tk.DISABLED
        )
        self.save_button.pack(side=tk.LEFT, padx=5, pady=5)
        
        self.time_label = ttk.Label(control_frame, text="00:00:00")
        self.time_label.pack(side=tk.RIGHT, padx=5, pady=5)
        
        # Audio level visualization
        level_frame = ttk.LabelFrame(main_frame, text="Audio Level")
        level_frame.pack(fill=tk.X, pady=10)
        
        self.level_canvas = tk.Canvas(level_frame, height=30, bg="white")
        self.level_canvas.pack(fill=tk.X, padx=5, pady=5)
        
        self.level_label = ttk.Label(level_frame, text="Level: 0.0")
        self.level_label.pack(pady=5)
        
        # Status bar
        self.status_bar = ttk.Label(
            self.root, 
            text="Ready", 
            relief=tk.SUNKEN, 
            anchor=tk.W
        )
        self.status_bar.pack(side=tk.BOTTOM, fill=tk.X)
    
    def detect_audio_devices(self):
        """Detect available audio devices"""
        if not AUDIO_AVAILABLE:
            self.status_bar.config(text="Audio modules not available")
            return
        
        try:
            p = pyaudio.PyAudio()
            device_count = p.get_device_count()
            devices = []
            
            # Try to get default input device
            try:
                default_device = p.get_default_input_device_info()
                logger.info(f"Default input device: {default_device['name']} (index: {default_device['index']})")
                self.selected_device_index = default_device['index']
            except Exception as e:
                logger.warning(f"Error getting default input device: {str(e)}")
                self.selected_device_index = None
            
            # Get all input devices
            for i in range(device_count):
                try:
                    device_info = p.get_device_info_by_index(i)
                    if device_info.get('maxInputChannels', 0) > 0:
                        is_default = i == self.selected_device_index
                        devices.append(f"{i}: {device_info.get('name')}" + (" (Default)" if is_default else ""))
                        
                        # If no default device was found, use the first one with input channels
                        if self.selected_device_index is None:
                            self.selected_device_index = i
                            logger.info(f"Using device {i} as fallback")
                except Exception as e:
                    logger.warning(f"Error getting info for device {i}: {str(e)}")
            
            # Update dropdown
            self.device_dropdown['values'] = devices
            if devices:
                self.device_dropdown.current(0)
                self.status_bar.config(text=f"Found {len(devices)} audio input devices")
            else:
                self.status_bar.config(text="No audio input devices found")
            
            # Clean up
            p.terminate()
            
        except Exception as e:
            logger.error(f"Error detecting audio devices: {str(e)}")
            self.status_bar.config(text=f"Error: {str(e)}")
    
    def on_device_selected(self, event):
        """Handle device selection"""
        if self.is_recording:
            messagebox.showwarning("Warning", "Please stop recording before changing the device")
            return
        
        selected = self.device_dropdown.get()
        if selected and ":" in selected:
            device_index = int(selected.split(":")[0])
            self.selected_device_index = device_index
            self.status_bar.config(text=f"Selected device: {selected}")
    
    def update_ui(self):
        """Periodically update the UI"""
        if self.is_recording:
            # Update recording time
            elapsed = time.time() - self.recording_start_time
            hours, remainder = divmod(int(elapsed), 3600)
            minutes, seconds = divmod(remainder, 60)
            self.time_label.config(text=f"{hours:02d}:{minutes:02d}:{seconds:02d}")
            
            # Update audio level visualization
            self.update_level_visualization(self.audio_level)
        
        # Schedule next update
        self.root.after(100, self.update_ui)
    
    def update_level_visualization(self, level):
        """Update the audio level visualization"""
        self.level_canvas.delete("all")
        width = self.level_canvas.winfo_width()
        height = self.level_canvas.winfo_height()
        
        # Draw background
        self.level_canvas.create_rectangle(0, 0, width, height, fill="white")
        
        # Draw level bar
        bar_width = int(width * (level / 100))
        
        # Color based on level
        if level < 30:
            color = "green"
        elif level < 70:
            color = "yellow"
        else:
            color = "red"
        
        self.level_canvas.create_rectangle(0, 0, bar_width, height, fill=color)
        self.level_label.config(text=f"Level: {level:.1f}")
    
    def toggle_recording(self):
        """Start or stop recording"""
        if not self.is_recording:
            self.start_recording()
        else:
            self.stop_recording()
    
    def start_recording(self):
        """Start recording audio"""
        if not AUDIO_AVAILABLE:
            messagebox.showerror("Error", "Audio modules not available")
            return
        
        if self.is_recording:
            return
        
        if self.selected_device_index is None:
            messagebox.showerror("Error", "No audio device selected")
            return
        
        try:
            # Initialize PyAudio
            self.audio_capture = pyaudio.PyAudio()
            
            # Open stream in blocking mode (no callback)
            self.stream = self.audio_capture.open(
                format=self.format,
                channels=self.channels,
                rate=self.sample_rate,
                input=True,
                input_device_index=self.selected_device_index,
                frames_per_buffer=self.chunk_size
            )
            
            # Reset audio data
            self.audio_data = []
            
            # Start recording thread
            self.is_recording = True
            self.recording_start_time = time.time()
            self.recording_thread = threading.Thread(target=self._record_audio, daemon=True)
            self.recording_thread.start()
            
            # Update UI
            self.record_button.config(text="Stop Recording")
            self.save_button.config(state=tk.DISABLED)
            self.status_bar.config(text="Recording...")
            
            logger.info(f"Started recording with device index {self.selected_device_index}")
            
        except Exception as e:
            logger.error(f"Error starting recording: {str(e)}")
            messagebox.showerror("Error", f"Failed to start recording: {str(e)}")
            self.cleanup()
    
    def _record_audio(self):
        """Record audio in a separate thread"""
        try:
            while self.is_recording and self.stream:
                try:
                    # Read audio data
                    data = self.stream.read(self.chunk_size, exception_on_overflow=False)
                    self.audio_data.append(data)
                    
                    # Calculate audio level
                    audio_array = np.frombuffer(data, dtype=np.int16)
                    level = np.abs(audio_array).mean()
                    normalized_level = min(100, level / 50)  # Normalize to 0-100
                    self.audio_level = normalized_level
                    
                    logger.debug(f"Audio level: {level:.2f} (normalized: {normalized_level:.2f})")
                    
                except Exception as e:
                    logger.error(f"Error reading audio: {str(e)}")
                    time.sleep(0.1)  # Avoid tight loop on errors
        except Exception as e:
            logger.error(f"Error in recording thread: {str(e)}")
        finally:
            logger.info("Recording thread finished")
    
    def stop_recording(self):
        """Stop recording audio"""
        if not self.is_recording:
            return
        
        try:
            # Stop recording
            self.is_recording = False
            
            # Wait for thread to finish
            if self.recording_thread and self.recording_thread.is_alive():
                self.recording_thread.join(timeout=2.0)
            
            # Close stream
            if self.stream:
                try:
                    self.stream.stop_stream()
                    self.stream.close()
                    self.stream = None
                except Exception as e:
                    logger.error(f"Error closing stream: {str(e)}")
            
            # Clean up PyAudio
            if self.audio_capture:
                self.audio_capture.terminate()
                self.audio_capture = None
            
            # Update UI
            self.record_button.config(text="Start Recording")
            self.save_button.config(state=tk.NORMAL if self.audio_data else tk.DISABLED)
            self.status_bar.config(text="Recording stopped")
            self.audio_level = 0
            
            logger.info("Stopped recording")
            
        except Exception as e:
            logger.error(f"Error stopping recording: {str(e)}")
            messagebox.showerror("Error", f"Error stopping recording: {str(e)}")
        finally:
            self.cleanup()
    
    def save_recording(self):
        """Save the recorded audio to a file"""
        if not self.audio_data:
            messagebox.showinfo("Info", "No audio data to save")
            return
        
        try:
            # Ask for save location
            filename = filedialog.asksaveasfilename(
                defaultextension=".wav",
                filetypes=[("WAV files", "*.wav")],
                initialfile=f"recording_{datetime.now().strftime('%Y%m%d_%H%M%S')}.wav"
            )
            
            if not filename:
                return
            
            # Save to WAV file
            with wave.open(filename, 'wb') as wf:
                wf.setnchannels(self.channels)
                wf.setsampwidth(self.audio_capture.get_sample_size(self.format) if self.audio_capture else 2)
                wf.setframerate(self.sample_rate)
                wf.writeframes(b''.join(self.audio_data))
            
            messagebox.showinfo("Success", f"Recording saved to {filename}")
            logger.info(f"Saved recording to {filename}")
            
        except Exception as e:
            logger.error(f"Error saving recording: {str(e)}")
            messagebox.showerror("Error", f"Failed to save recording: {str(e)}")
    
    def cleanup(self):
        """Clean up resources"""
        if self.stream:
            try:
                self.stream.stop_stream()
                self.stream.close()
                self.stream = None
            except:
                pass
        
        if self.audio_capture:
            try:
                self.audio_capture.terminate()
                self.audio_capture = None
            except:
                pass
    
    def __del__(self):
        self.cleanup()

def main():
    """Main entry point"""
    try:
        # Create the root window
        root = tk.Tk()
        
        # Create and run the application
        app = SimpleRecorder(root)
        
        # Handle window close
        def on_closing():
            if app.is_recording:
                if messagebox.askyesno("Confirm", "Recording is in progress. Stop recording and exit?"):
                    app.stop_recording()
                    root.destroy()
            else:
                root.destroy()
        
        root.protocol("WM_DELETE_WINDOW", on_closing)
        root.mainloop()
        
    except Exception as e:
        logger.critical(f"Unhandled exception: {str(e)}")
        messagebox.showerror("Critical Error", f"An unhandled error occurred: {str(e)}")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
