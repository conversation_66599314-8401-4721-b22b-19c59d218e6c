#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import sys
import shutil
import subprocess
import time
from pathlib import Path

def build_executable():
    """Build the executable using PyInstaller"""
    print("Building SumOnTheFly executable...")
    
    # Clean any previous build directories
    for dir_name in ['build', 'dist']:
        if os.path.exists(dir_name):
            print(f"Cleaning {dir_name} directory...")
            try:
                shutil.rmtree(dir_name)
            except PermissionError as e:
                print(f"Warning: Could not remove {dir_name} directory: {e}")
                print(f"The executable might be in use. Please close it and try again.")
                # Try to rename the dist directory instead
                if dir_name == 'dist':
                    try:
                        backup_dir = f"{dir_name}_old_{int(time.time())}"
                        print(f"Renaming {dir_name} to {backup_dir}")
                        os.rename(dir_name, backup_dir)
                    except Exception as rename_err:
                        print(f"Could not rename directory: {rename_err}")
                        print("Will attempt to build anyway...")
    
    # PyInstaller command
    cmd = [
        'pyinstaller',
        '--name=SumOnTheFly',
        '--onefile',  # Create a single executable file
        '--windowed',  # Don't show console window on Windows
        '--add-data=README.md;.',  # Include README file
        '--hidden-import=PIL._tkinter_finder',  # Fix potential Pillow/Tkinter issues
        '--hidden-import=sounddevice',  # Add sounddevice module
        '--hidden-import=pyaudio',  # Add pyaudio module
        '--hidden-import=numpy',  # Add numpy module
        '--hidden-import=wave',  # Add wave module
        '--hidden-import=sqlite3',  # Add sqlite3 module
        '--hidden-import=docx',  # Add python-docx module
        '--hidden-import=reportlab',  # Add reportlab module
        '--hidden-import=pydub',  # Add pydub module
        '--hidden-import=threading',  # Add threading module
        '--hidden-import=tkinter',  # Add tkinter module
        'app.py'  # Main script
    ]
    
    # Execute PyInstaller
    print("Running PyInstaller...")
    print(f"Command: {' '.join(cmd)}")
    result = subprocess.run(cmd, capture_output=False)  # Show output in real-time
    
    if result.returncode != 0:
        print("Error building executable")
        return False
    
    print("PyInstaller completed successfully.")
    
    # Check if executable was created
    exe_path = os.path.join('dist', 'SumOnTheFly.exe')
    if os.path.exists(exe_path):
        print(f"Executable created at: {os.path.abspath(exe_path)}")
        return True
    else:
        print("Executable was not created. Check the PyInstaller output.")
        return False

if __name__ == "__main__":
    success = build_executable()
    sys.exit(0 if success else 1)
