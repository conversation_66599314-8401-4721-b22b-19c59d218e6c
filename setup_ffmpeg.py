#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import sys
import subprocess
import platform

def check_ffmpeg():
    """Check if FFmpeg is available in PATH"""
    try:
        subprocess.run(["ffmpeg", "-version"], stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        print("✅ FFmpeg is already installed and in your PATH")
        return True
    except FileNotFoundError:
        print("❌ FFmpeg is not found in your PATH")
        return False

def setup_ffmpeg_instructions():
    """Provide instructions for setting up FFmpeg"""
    print("\nTo set up FFmpeg for the Whisper transcriber:")
    print("\n1. Download FFmpeg:")
    print("   - Windows: https://www.gyan.dev/ffmpeg/builds/ffmpeg-release-essentials.zip")
    print("   - macOS: Use Homebrew with 'brew install ffmpeg'")
    print("   - Linux: Use your package manager, e.g., 'sudo apt install ffmpeg'")
    
    print("\n2. Extract the downloaded archive (for Windows)")
    
    print("\n3. Add FFmpeg to your PATH:")
    print("   - Windows: ")
    print("     a. Right-click on 'This PC' or 'My Computer' and select 'Properties'")
    print("     b. Click on 'Advanced system settings'")
    print("     c. Click on 'Environment Variables'")
    print("     d. Under 'System variables', find the 'Path' variable, select it and click 'Edit'")
    print("     e. Click 'New' and add the path to the FFmpeg bin folder (e.g., C:\\ffmpeg\\bin)")
    print("     f. Click 'OK' on all dialogs to save the changes")
    print("   - macOS/Linux: The package manager should handle this automatically")
    
    print("\n4. Restart your command prompt or terminal to apply the PATH changes")
    
    print("\n5. Verify the installation by running 'ffmpeg -version'")
    
    print("\nOnce FFmpeg is properly installed, you can run the Whisper transcriber:")
    print("python whisper_transcriber.py")

def main():
    """Main function"""
    print("FFmpeg Setup Helper for Whisper Transcriber")
    print("===========================================\n")
    
    # Check if FFmpeg is already installed
    if check_ffmpeg():
        print("\nYou can now run the Whisper transcriber:")
        print("python whisper_transcriber.py")
        return 0
    
    # Provide setup instructions
    setup_ffmpeg_instructions()
    return 1

if __name__ == "__main__":
    sys.exit(main())
