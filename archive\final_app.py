#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import pyaudio
import wave
import numpy as np
import time
import os
import threading
import json
import requests
import sqlite3
from datetime import datetime
import logging
import sys

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("sumonthefly.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# Check for required modules
try:
    import pyaudio
    AUDIO_AVAILABLE = True
    logger.info("PyAudio is available")
except ImportError:
    AUDIO_AVAILABLE = False
    logger.error("PyAudio is not available - audio capture will not work")

try:
    from reportlab.lib.pagesizes import letter
    from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
    from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer
    REPORTLAB_AVAILABLE = True
    logger.info("reportlab is available - PDF export enabled")
except ImportError:
    REPORTLAB_AVAILABLE = False
    logger.warning("reportlab is not available - PDF export disabled")

try:
    import docx
    DOCX_AVAILABLE = True
    logger.info("python-docx is available - Word export enabled")
except ImportError:
    DOCX_AVAILABLE = False
    logger.warning("python-docx is not available - Word export disabled")

class Config:
    """Configuration manager"""
    
    def __init__(self, config_file="final_config.json"):
        self.config_file = config_file
        self.config = {
            # Ollama settings
            "ollama_host": "http://CarlsMacStudio.got.volvo.net:11434",
            "transcription_model": "dimavz/whisper-tiny",
            "summarization_model": "qwen3",
            
            # Audio settings
            "sample_rate": 16000,
            "channels": 1,
            "chunk_size": 1024,
            
            # Application settings
            "summary_interval": 30,  # seconds
        }
        self.load()
    
    def load(self):
        """Load configuration from file"""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r') as f:
                    loaded_config = json.load(f)
                    self.config.update(loaded_config)
                logger.info(f"Loaded configuration from {self.config_file}")
        except Exception as e:
            logger.error(f"Error loading configuration: {str(e)}")
    
    def save(self):
        """Save configuration to file"""
        try:
            with open(self.config_file, 'w') as f:
                json.dump(self.config, f, indent=4)
            logger.info(f"Saved configuration to {self.config_file}")
            return True
        except Exception as e:
            logger.error(f"Error saving configuration: {str(e)}")
            return False
    
    def get(self, key, default=None):
        """Get a configuration value"""
        return self.config.get(key, default)
    
    def set(self, key, value):
        """Set a configuration value"""
        self.config[key] = value

class DatabaseManager:
    """Database manager for storing session data"""
    
    def __init__(self, db_path=None):
        # Default database location
        if db_path is None:
            data_dir = os.path.join(os.path.expanduser("~"), "SumOnTheFly")
            os.makedirs(data_dir, exist_ok=True)
            db_path = os.path.join(data_dir, "sumonthefly.db")
        
        self.db_path = db_path
        self._initialize_db()
    
    def _initialize_db(self):
        """Initialize the database schema"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # Create sessions table
            cursor.execute('''
            CREATE TABLE IF NOT EXISTS sessions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                start_time REAL NOT NULL,
                end_time REAL,
                metadata TEXT
            )
            ''')
            
            # Create transcriptions table
            cursor.execute('''
            CREATE TABLE IF NOT EXISTS transcriptions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                session_id INTEGER NOT NULL,
                text TEXT NOT NULL,
                timestamp REAL NOT NULL,
                FOREIGN KEY (session_id) REFERENCES sessions (id) ON DELETE CASCADE
            )
            ''')
            
            # Create summaries table
            cursor.execute('''
            CREATE TABLE IF NOT EXISTS summaries (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                session_id INTEGER NOT NULL,
                text TEXT NOT NULL,
                timestamp REAL NOT NULL,
                FOREIGN KEY (session_id) REFERENCES sessions (id) ON DELETE CASCADE
            )
            ''')
            
            # Enable foreign key support
            cursor.execute("PRAGMA foreign_keys = ON")
            
            conn.commit()
            conn.close()
            
            logger.info(f"Database initialized at {self.db_path}")
            
        except Exception as e:
            logger.error(f"Error initializing database: {str(e)}")
            raise
    
    def create_session(self, name, metadata=None):
        """Create a new recording session"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            start_time = datetime.now().timestamp()
            metadata_json = json.dumps(metadata) if metadata else None
            
            cursor.execute(
                "INSERT INTO sessions (name, start_time, metadata) VALUES (?, ?, ?)",
                (name, start_time, metadata_json)
            )
            
            session_id = cursor.lastrowid
            conn.commit()
            conn.close()
            
            logger.info(f"Created new session: {name} (ID: {session_id})")
            return session_id
            
        except Exception as e:
            logger.error(f"Error creating session: {str(e)}")
            raise
    
    def update_session_end_time(self, session_id):
        """Update the end time of a session"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            end_time = datetime.now().timestamp()
            
            cursor.execute(
                "UPDATE sessions SET end_time = ? WHERE id = ?",
                (end_time, session_id)
            )
            
            conn.commit()
            conn.close()
            
            logger.info(f"Updated end time for session ID: {session_id}")
            return True
            
        except Exception as e:
            logger.error(f"Error updating session end time: {str(e)}")
            return False
    
    def add_transcription(self, session_id, text, timestamp):
        """Add a transcription to a session"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute(
                "INSERT INTO transcriptions (session_id, text, timestamp) VALUES (?, ?, ?)",
                (session_id, text, timestamp)
            )
            
            transcription_id = cursor.lastrowid
            conn.commit()
            conn.close()
            
            return transcription_id
            
        except Exception as e:
            logger.error(f"Error adding transcription: {str(e)}")
            return -1
    
    def add_summary(self, session_id, text, timestamp):
        """Add a summary to a session"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute(
                "INSERT INTO summaries (session_id, text, timestamp) VALUES (?, ?, ?)",
                (session_id, text, timestamp)
            )
            
            summary_id = cursor.lastrowid
            conn.commit()
            conn.close()
            
            return summary_id
            
        except Exception as e:
            logger.error(f"Error adding summary: {str(e)}")
            return -1
    
    def get_session_data(self, session_id):
        """Get all data for a session"""
        try:
            conn = sqlite3.connect(self.db_path)
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()
            
            # Get session info
            cursor.execute("SELECT * FROM sessions WHERE id = ?", (session_id,))
            session_row = cursor.fetchone()
            if not session_row:
                conn.close()
                return None
                
            session = dict(session_row)
            
            # Get transcriptions
            cursor.execute(
                "SELECT * FROM transcriptions WHERE session_id = ? ORDER BY timestamp",
                (session_id,)
            )
            transcriptions = [dict(row) for row in cursor.fetchall()]
            
            # Get summaries
            cursor.execute(
                "SELECT * FROM summaries WHERE session_id = ? ORDER BY timestamp",
                (session_id,)
            )
            summaries = [dict(row) for row in cursor.fetchall()]
            
            conn.close()
            
            # Parse metadata
            if session.get('metadata'):
                try:
                    session['metadata'] = json.loads(session['metadata'])
                except:
                    session['metadata'] = {}
            
            # Combine all data
            return {
                "session": session,
                "transcriptions": transcriptions,
                "summaries": summaries
            }
            
        except Exception as e:
            logger.error(f"Error getting session data: {str(e)}")
            return None

class TranscriptionService:
    """Service for transcribing audio using Ollama"""
    
    def __init__(self, ollama_host, model="whisper"):
        self.ollama_host = ollama_host
        self.model = model
        self.is_processing = False
        self.audio_buffer = []
        self.buffer_lock = threading.Lock()
        self.test_connection()
    
    def test_connection(self):
        """Test connection to Ollama server"""
        try:
            response = requests.get(f"{self.ollama_host}/api/version")
            if response.status_code == 200:
                version = response.json().get("version", "unknown")
                logger.info(f"Connected to Ollama server (version {version})")
                return True
            else:
                logger.warning(f"Failed to connect to Ollama server: {response.status_code}")
                return False
        except Exception as e:
            logger.warning(f"Error connecting to Ollama server: {str(e)}")
            return False
    
    def process_audio(self, audio_data, callback):
        """Process audio data and get transcription"""
        with self.buffer_lock:
            self.audio_buffer.append(audio_data)
        
        if not self.is_processing:
            self._start_processing(callback)
    
    def _start_processing(self, callback):
        """Start processing audio buffer"""
        self.is_processing = True
        
        # Get audio data from buffer
        with self.buffer_lock:
            if not self.audio_buffer:
                self.is_processing = False
                return
            
            # Combine all chunks in the buffer
            audio_data = np.concatenate(self.audio_buffer)
            self.audio_buffer = []
        
        # Start transcription in a separate thread
        thread = threading.Thread(
            target=self._transcribe_audio,
            args=(audio_data, callback),
            daemon=True
        )
        thread.start()
    
    def _transcribe_audio(self, audio_data, callback):
        """Transcribe audio data using Ollama"""
        try:
            # Convert audio to WAV format in memory
            import io
            audio_buffer = io.BytesIO()
            
            with wave.open(audio_buffer, 'wb') as wf:
                wf.setnchannels(1)
                wf.setsampwidth(2)  # 16-bit audio
                wf.setframerate(16000)
                wf.writeframes(audio_data.tobytes())
            
            audio_bytes = audio_buffer.getvalue()
            
            # Send to Ollama for transcription
            url = f"{self.ollama_host}/api/audio"
            files = {
                'file': ('audio.wav', audio_bytes, 'audio/wav')
            }
            data = {
                'model': self.model,
                'format': 'json',
                'language': 'en'
            }
            
            response = requests.post(url, files=files, data=data)
            
            if response.status_code == 200:
                result = response.json()
                text = result.get('text', '')
                if text and callback:
                    callback(text)
            else:
                logger.warning(f"Transcription failed: {response.status_code} - {response.text}")
                
        except Exception as e:
            logger.error(f"Error in transcription: {str(e)}")
        finally:
            self.is_processing = False
            # Process any remaining audio in the buffer
            with self.buffer_lock:
                if self.audio_buffer and callback:
                    self._start_processing(callback)

class SummarizationService:
    """Service for summarizing text using Ollama"""
    
    def __init__(self, ollama_host, model="llama2"):
        self.ollama_host = ollama_host
        self.model = model
        self.test_connection()
    
    def test_connection(self):
        """Test connection to Ollama server"""
        try:
            response = requests.get(f"{self.ollama_host}/api/version")
            if response.status_code == 200:
                version = response.json().get("version", "unknown")
                logger.info(f"Connected to Ollama server (version {version})")
                return True
            else:
                logger.warning(f"Failed to connect to Ollama server: {response.status_code}")
                return False
        except Exception as e:
            logger.warning(f"Error connecting to Ollama server: {str(e)}")
            return False
    
    def summarize_text(self, text, prompt_template, callback=None):
        """Summarize text using Ollama"""
        thread = threading.Thread(
            target=self._generate_summary,
            args=(text, prompt_template, callback),
            daemon=True
        )
        thread.start()
    
    def _generate_summary(self, text, prompt_template, callback):
        """Generate summary using Ollama"""
        try:
            # Prepare prompt
            prompt = f"{prompt_template}\n\nText to summarize:\n{text}"
            
            # Send to Ollama
            url = f"{self.ollama_host}/api/generate"
            data = {
                "model": self.model,
                "prompt": prompt,
                "stream": False
            }
            
            response = requests.post(url, json=data)
            
            if response.status_code == 200:
                result = response.json()
                summary = result.get('response', '')
                if summary and callback:
                    callback(summary)
            else:
                logger.warning(f"Summarization failed: {response.status_code} - {response.text}")
                
        except Exception as e:
            logger.error(f"Error in summarization: {str(e)}")

class ExportService:
    """Service for exporting session data to various formats"""
    
    def export_session(self, filename, session_data):
        """Export session data to the specified file format"""
        if not session_data:
            logger.error("No session data to export")
            return False
        
        file_ext = os.path.splitext(filename)[1].lower()
        
        try:
            if file_ext == ".pdf" and REPORTLAB_AVAILABLE:
                return self._export_to_pdf(filename, session_data)
            elif file_ext == ".docx" and DOCX_AVAILABLE:
                return self._export_to_docx(filename, session_data)
            elif file_ext == ".txt":
                return self._export_to_text(filename, session_data)
            else:
                # Fall back to text export
                text_filename = os.path.splitext(filename)[0] + ".txt"
                logger.warning(f"Unsupported export format: {file_ext}. Falling back to text export: {text_filename}")
                return self._export_to_text(text_filename, session_data)
        except Exception as e:
            logger.error(f"Error exporting session: {str(e)}")
            return False
    
    def _export_to_pdf(self, filename, session_data):
        """Export session data to PDF format"""
        try:
            doc = SimpleDocTemplate(filename, pagesize=letter)
            styles = getSampleStyleSheet()
            story = []
            
            # Add title
            session_name = session_data["session"]["name"]
            title_style = styles["Title"]
            story.append(Paragraph(f"Session: {session_name}", title_style))
            story.append(Spacer(1, 12))
            
            # Add session info
            session = session_data["session"]
            start_time = datetime.fromtimestamp(session["start_time"]).strftime("%Y-%m-%d %H:%M:%S")
            end_time = datetime.fromtimestamp(session["end_time"]).strftime("%Y-%m-%d %H:%M:%S") if session["end_time"] else "N/A"
            
            info_style = styles["Normal"]
            story.append(Paragraph(f"Start Time: {start_time}", info_style))
            story.append(Paragraph(f"End Time: {end_time}", info_style))
            story.append(Spacer(1, 12))
            
            # Add summaries section
            if session_data["summaries"]:
                story.append(Paragraph("Summaries", styles["Heading1"]))
                story.append(Spacer(1, 6))
                
                for summary in session_data["summaries"]:
                    timestamp = datetime.fromtimestamp(summary["timestamp"]).strftime("%H:%M:%S")
                    story.append(Paragraph(f"[{timestamp}]", styles["Heading2"]))
                    story.append(Paragraph(summary["text"], styles["Normal"]))
                    story.append(Spacer(1, 12))
            
            # Add transcriptions section
            if session_data["transcriptions"]:
                story.append(Paragraph("Transcriptions", styles["Heading1"]))
                story.append(Spacer(1, 6))
                
                full_transcript = ""
                for transcription in session_data["transcriptions"]:
                    timestamp = datetime.fromtimestamp(transcription["timestamp"]).strftime("%H:%M:%S")
                    full_transcript += f"[{timestamp}] {transcription['text']} "
                
                story.append(Paragraph(full_transcript, styles["Normal"]))
            
            # Build PDF
            doc.build(story)
            logger.info(f"Exported session to PDF: {filename}")
            return True
            
        except Exception as e:
            logger.error(f"Error exporting to PDF: {str(e)}")
            return False
    
    def _export_to_docx(self, filename, session_data):
        """Export session data to DOCX format"""
        try:
            doc = docx.Document()
            
            # Add title
            session_name = session_data["session"]["name"]
            doc.add_heading(f"Session: {session_name}", 0)
            
            # Add session info
            session = session_data["session"]
            start_time = datetime.fromtimestamp(session["start_time"]).strftime("%Y-%m-%d %H:%M:%S")
            end_time = datetime.fromtimestamp(session["end_time"]).strftime("%Y-%m-%d %H:%M:%S") if session["end_time"] else "N/A"
            
            doc.add_paragraph(f"Start Time: {start_time}")
            doc.add_paragraph(f"End Time: {end_time}")
            doc.add_paragraph()
            
            # Add summaries section
            if session_data["summaries"]:
                doc.add_heading("Summaries", 1)
                
                for summary in session_data["summaries"]:
                    timestamp = datetime.fromtimestamp(summary["timestamp"]).strftime("%H:%M:%S")
                    doc.add_heading(f"[{timestamp}]", 2)
                    doc.add_paragraph(summary["text"])
            
            # Add transcriptions section
            if session_data["transcriptions"]:
                doc.add_heading("Transcriptions", 1)
                
                full_transcript = ""
                for transcription in session_data["transcriptions"]:
                    timestamp = datetime.fromtimestamp(transcription["timestamp"]).strftime("%H:%M:%S")
                    full_transcript += f"[{timestamp}] {transcription['text']} "
                
                doc.add_paragraph(full_transcript)
            
            # Save document
            doc.save(filename)
            logger.info(f"Exported session to DOCX: {filename}")
            return True
            
        except Exception as e:
            logger.error(f"Error exporting to DOCX: {str(e)}")
            return False
    
    def _export_to_text(self, filename, session_data):
        """Export session data to text format"""
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                # Add title
                session_name = session_data["session"]["name"]
                f.write(f"Session: {session_name}\n")
                f.write("=" * 50 + "\n\n")
                
                # Add session info
                session = session_data["session"]
                start_time = datetime.fromtimestamp(session["start_time"]).strftime("%Y-%m-%d %H:%M:%S")
                end_time = datetime.fromtimestamp(session["end_time"]).strftime("%Y-%m-%d %H:%M:%S") if session["end_time"] else "N/A"
                
                f.write(f"Start Time: {start_time}\n")
                f.write(f"End Time: {end_time}\n\n")
                
                # Add summaries section
                if session_data["summaries"]:
                    f.write("SUMMARIES\n")
                    f.write("-" * 50 + "\n\n")
                    
                    for summary in session_data["summaries"]:
                        timestamp = datetime.fromtimestamp(summary["timestamp"]).strftime("%H:%M:%S")
                        f.write(f"[{timestamp}]\n")
                        f.write(f"{summary['text']}\n\n")
                
                # Add transcriptions section
                if session_data["transcriptions"]:
                    f.write("TRANSCRIPTIONS\n")
                    f.write("-" * 50 + "\n\n")
                    
                    for transcription in session_data["transcriptions"]:
                        timestamp = datetime.fromtimestamp(transcription["timestamp"]).strftime("%H:%M:%S")
                        f.write(f"[{timestamp}] {transcription['text']} ")
            
            logger.info(f"Exported session to text: {filename}")
            return True
            
        except Exception as e:
            logger.error(f"Error exporting to text: {str(e)}")
            return False

class SumOnTheFlyApp:
    """Main application class for SumOnTheFly"""
    
    def __init__(self, root):
        self.root = root
        self.root.title("SumOnTheFly")
        self.root.geometry("1024x768")
        self.root.minsize(800, 600)
        
        # Initialize components
        self.config = Config()
        self.db = DatabaseManager()
        self.export_service = ExportService()
        
        # State variables
        self.is_recording = False
        self.current_session_id = None
        self.transcription_buffer = ""
        self.last_summary_time = 0
        self.recording_start_time = 0
        self.audio_level = 0
        
        # Audio components
        self.p = None
        self.stream = None
        self.frames = []
        self.device_indices = []
        self.recording_thread = None
        
        # Services
        self.transcription_service = None
        self.summarization_service = None
        
        # Set up UI
        self.setup_ui()
        
        # Detect audio devices
        self.detect_audio_devices()
        
        # Start UI update timer
        self.update_ui()
    
    def setup_ui(self):
        """Set up the main UI components"""
        # Main frame
        main_frame = ttk.Frame(self.root, padding=10)
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # Create menu
        self.create_menu()
        
        # Device selection
        device_frame = ttk.LabelFrame(main_frame, text="Audio Device")
        device_frame.pack(fill=tk.X, pady=5)
        
        self.device_var = tk.StringVar()
        self.device_dropdown = ttk.Combobox(device_frame, textvariable=self.device_var, state="readonly")
        self.device_dropdown.pack(side=tk.LEFT, padx=5, pady=5, fill=tk.X, expand=True)
        self.device_dropdown.bind("<<ComboboxSelected>>", self.on_device_selected)
        
        refresh_button = ttk.Button(device_frame, text="Refresh", command=self.detect_audio_devices)
        refresh_button.pack(side=tk.RIGHT, padx=5, pady=5)
        
        # Control panel
        control_frame = ttk.LabelFrame(main_frame, text="Controls")
        control_frame.pack(fill=tk.X, pady=5)
        
        self.record_button = ttk.Button(
            control_frame, 
            text="Start Recording", 
            command=self.toggle_recording
        )
        self.record_button.pack(side=tk.LEFT, padx=5, pady=5)
        
        self.session_label = ttk.Label(control_frame, text="No active session")
        self.session_label.pack(side=tk.LEFT, padx=5, pady=5)
        
        self.time_label = ttk.Label(control_frame, text="00:00:00")
        self.time_label.pack(side=tk.RIGHT, padx=5, pady=5)
        
        # Audio level visualization
        level_frame = ttk.Frame(control_frame)
        level_frame.pack(side=tk.RIGHT, padx=5, pady=5, fill=tk.X, expand=True)
        
        ttk.Label(level_frame, text="Audio Level:").pack(side=tk.LEFT)
        
        self.level_canvas = tk.Canvas(level_frame, width=100, height=20, bg="white")
        self.level_canvas.pack(side=tk.LEFT, padx=5, fill=tk.X, expand=True)
        
        # Transcription panel
        transcription_frame = ttk.LabelFrame(main_frame, text="Real-time Transcription")
        transcription_frame.pack(fill=tk.BOTH, expand=True, pady=5)
        
        self.transcription_text = tk.Text(
            transcription_frame,
            wrap=tk.WORD,
            state=tk.DISABLED
        )
        transcription_scroll = ttk.Scrollbar(
            transcription_frame, 
            command=self.transcription_text.yview
        )
        self.transcription_text.configure(yscrollcommand=transcription_scroll.set)
        
        self.transcription_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        transcription_scroll.pack(side=tk.RIGHT, fill=tk.Y)
        
        # Summary panel
        summary_frame = ttk.LabelFrame(main_frame, text="Summaries")
        summary_frame.pack(fill=tk.BOTH, expand=True, pady=5)
        
        self.summary_text = tk.Text(
            summary_frame,
            wrap=tk.WORD,
            state=tk.DISABLED,
            height=10
        )
        summary_scroll = ttk.Scrollbar(
            summary_frame, 
            command=self.summary_text.yview
        )
        self.summary_text.configure(yscrollcommand=summary_scroll.set)
        
        self.summary_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        summary_scroll.pack(side=tk.RIGHT, fill=tk.Y)
        
        # Status bar
        self.status_bar = ttk.Label(
            self.root, 
            text="Ready", 
            relief=tk.SUNKEN, 
            anchor=tk.W
        )
        self.status_bar.pack(side=tk.BOTTOM, fill=tk.X)
    
    def create_menu(self):
        """Create the application menu"""
        menubar = tk.Menu(self.root)
        
        # File menu
        file_menu = tk.Menu(menubar, tearoff=0)
        file_menu.add_command(label="New Session", command=self.new_session)
        file_menu.add_separator()
        file_menu.add_command(label="Export Session", command=self.export_session)
        file_menu.add_separator()
        file_menu.add_command(label="Exit", command=self.root.quit)
        menubar.add_cascade(label="File", menu=file_menu)
        
        # Settings menu
        settings_menu = tk.Menu(menubar, tearoff=0)
        settings_menu.add_command(label="Configure", command=self.show_settings)
        menubar.add_cascade(label="Settings", menu=settings_menu)
        
        # Help menu
        help_menu = tk.Menu(menubar, tearoff=0)
        help_menu.add_command(label="About", command=self.show_about)
        menubar.add_cascade(label="Help", menu=help_menu)
        
        self.root.config(menu=menubar)
    
    def detect_audio_devices(self):
        """Detect available audio devices"""
        if not AUDIO_AVAILABLE:
            self.status_bar.config(text="Audio capture not available - PyAudio not installed")
            return
        
        try:
            p = pyaudio.PyAudio()
            device_count = p.get_device_count()
            devices = []
            self.device_indices = []
            
            # Get all input devices
            for i in range(device_count):
                try:
                    device_info = p.get_device_info_by_index(i)
                    if device_info.get('maxInputChannels', 0) > 0:
                        devices.append(f"{device_info.get('name')}")
                        self.device_indices.append(i)
                except Exception as e:
                    logger.warning(f"Error getting info for device {i}: {str(e)}")
            
            # Update dropdown
            self.device_dropdown['values'] = devices
            if devices:
                self.device_dropdown.current(0)
                self.status_bar.config(text=f"Found {len(devices)} audio input devices")
            else:
                self.status_bar.config(text="No audio input devices found")
            
            # Clean up
            p.terminate()
            
        except Exception as e:
            logger.error(f"Error detecting audio devices: {str(e)}")
            self.status_bar.config(text=f"Error: {str(e)}")
    
    def on_device_selected(self, event):
        """Handle device selection"""
        if self.is_recording:
            messagebox.showwarning("Warning", "Please stop recording before changing the device")
            return
    
    def update_ui(self):
        """Periodically update the UI"""
        if self.is_recording:
            # Update recording time
            elapsed = time.time() - self.recording_start_time
            hours, remainder = divmod(int(elapsed), 3600)
            minutes, seconds = divmod(remainder, 60)
            self.time_label.config(text=f"{hours:02d}:{minutes:02d}:{seconds:02d}")
            
            # Update audio level visualization
            self.update_level_visualization(self.audio_level)
            
            # Check if it's time for a new summary
            summary_interval = self.config.get("summary_interval", 30)
            if elapsed - self.last_summary_time >= summary_interval and self.transcription_buffer.strip():
                self.generate_summary()
                self.last_summary_time = elapsed
        
        # Schedule the next update
        self.root.after(100, self.update_ui)
    
    def update_level_visualization(self, level):
        """Update the audio level visualization"""
        self.level_canvas.delete("all")
        width = self.level_canvas.winfo_width()
        height = self.level_canvas.winfo_height()
        
        # Draw background
        self.level_canvas.create_rectangle(0, 0, width, height, fill="white")
        
        # Draw level bar
        bar_width = int(width * (level / 100))
        
        # Color based on level
        if level < 30:
            color = "green"
        elif level < 70:
            color = "yellow"
        else:
            color = "red"
        
        self.level_canvas.create_rectangle(0, 0, bar_width, height, fill=color)
    
    def toggle_recording(self):
        """Start or stop recording"""
        if not self.is_recording:
            self.start_recording()
        else:
            self.stop_recording()
    
    def start_recording(self):
        """Start a new recording session"""
        if not AUDIO_AVAILABLE:
            messagebox.showerror("Error", "Audio capture not available - PyAudio not installed")
            return
        
        try:
            # Get selected device index
            selected_index = self.device_dropdown.current()
            if selected_index < 0 or selected_index >= len(self.device_indices):
                messagebox.showerror("Error", "No audio device selected")
                return
            
            device_index = self.device_indices[selected_index]
            
            # Create a new session
            session_name = f"Session_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            self.current_session_id = self.db.create_session(session_name)
            
            # Initialize PyAudio
            self.p = pyaudio.PyAudio()
            
            # Clear previous recording
            self.frames = []
            
            # Open stream
            self.stream = self.p.open(
                format=pyaudio.paInt16,
                channels=self.config.get("channels", 1),
                rate=self.config.get("sample_rate", 16000),
                input=True,
                input_device_index=device_index,
                frames_per_buffer=self.config.get("chunk_size", 1024)
            )
            
            # Initialize services
            self.transcription_service = TranscriptionService(
                ollama_host=self.config.get("ollama_host"),
                model=self.config.get("transcription_model")
            )
            
            self.summarization_service = SummarizationService(
                ollama_host=self.config.get("ollama_host"),
                model=self.config.get("summarization_model")
            )
            
            # Update state and UI
            self.is_recording = True
            self.recording_start_time = time.time()
            self.last_summary_time = 0
            self.transcription_buffer = ""
            self.record_button.config(text="Stop Recording")
            self.session_label.config(text=f"Active session: {session_name}")
            self.status_bar.config(text="Recording...")
            
            # Clear text areas
            self.transcription_text.config(state=tk.NORMAL)
            self.transcription_text.delete(1.0, tk.END)
            self.transcription_text.config(state=tk.DISABLED)
            
            self.summary_text.config(state=tk.NORMAL)
            self.summary_text.delete(1.0, tk.END)
            self.summary_text.config(state=tk.DISABLED)
            
            # Start recording thread
            self.recording_thread = threading.Thread(target=self._record_audio)
            self.recording_thread.daemon = True
            self.recording_thread.start()
            
            logger.info(f"Started recording session: {session_name}")
            
        except Exception as e:
            logger.error(f"Error starting recording: {str(e)}")
            messagebox.showerror("Error", f"Failed to start recording: {str(e)}")
            self.cleanup()
    
    def _record_audio(self):
        """Record audio in a separate thread"""
        try:
            while self.is_recording and self.stream:
                try:
                    # Read audio data
                    data = self.stream.read(self.config.get("chunk_size", 1024), exception_on_overflow=False)
                    self.frames.append(data)
                    
                    # Calculate audio level
                    audio_array = np.frombuffer(data, dtype=np.int16)
                    level = np.abs(audio_array).mean()
                    normalized_level = min(100, level / 50)  # Normalize to 0-100
                    self.audio_level = normalized_level
                    
                    # Send to transcription service
                    if self.transcription_service:
                        self.transcription_service.process_audio(audio_array, self.on_transcription)
                    
                except Exception as e:
                    logger.error(f"Error reading audio: {str(e)}")
                    time.sleep(0.1)  # Avoid tight loop on errors
        except Exception as e:
            logger.error(f"Error in recording thread: {str(e)}")
        finally:
            logger.info("Recording thread finished")
    
    def stop_recording(self):
        """Stop the current recording session"""
        if not self.is_recording:
            return
        
        try:
            # Signal recording thread to stop
            self.is_recording = False
            
            # Wait for recording thread to finish (with timeout)
            if self.recording_thread and self.recording_thread.is_alive():
                self.recording_thread.join(timeout=1.0)
            
            # Cleanup in a separate thread to avoid UI freezing
            cleanup_thread = threading.Thread(target=self._cleanup_async)
            cleanup_thread.daemon = True
            cleanup_thread.start()
            
            # Generate final summary if needed
            if self.transcription_buffer.strip():
                self.generate_summary()
            
            # Update UI
            self.record_button.config(text="Start Recording")
            self.status_bar.config(text="Ready")
            
            # Save session data
            if self.current_session_id:
                self.db.update_session_end_time(self.current_session_id)
            
            logger.info("Stopped recording session")
            
        except Exception as e:
            logger.error(f"Error stopping recording: {str(e)}")
            messagebox.showerror("Error", f"Error stopping recording: {str(e)}")
    
    def _cleanup_async(self):
        """Clean up resources in a separate thread"""
        try:
            # Close stream
            if self.stream:
                try:
                    self.stream.stop_stream()
                    self.stream.close()
                except Exception as e:
                    logger.error(f"Error closing stream: {e}")
                finally:
                    self.stream = None
            
            # Terminate PyAudio
            if self.p:
                try:
                    self.p.terminate()
                except Exception as e:
                    logger.error(f"Error terminating PyAudio: {e}")
                finally:
                    self.p = None
            
            # Clean up services
            self.transcription_service = None
            self.summarization_service = None
            
            logger.info("Cleanup completed successfully")
            
        except Exception as e:
            logger.error(f"Error in cleanup: {e}")
    
    def cleanup(self):
        """Clean up resources"""
        self.is_recording = False
        
        if self.stream:
            try:
                self.stream.stop_stream()
                self.stream.close()
            except:
                pass
            finally:
                self.stream = None
        
        if self.p:
            try:
                self.p.terminate()
            except:
                pass
            finally:
                self.p = None
        
        self.transcription_service = None
        self.summarization_service = None
    
    def on_transcription(self, text):
        """Handle new transcription text"""
        if not text or not self.is_recording:
            return
        
        # Update the transcription buffer
        self.transcription_buffer += text + " "
        
        # Update the UI
        self.transcription_text.config(state=tk.NORMAL)
        self.transcription_text.insert(tk.END, text + " ")
        self.transcription_text.see(tk.END)
        self.transcription_text.config(state=tk.DISABLED)
        
        # Save to database
        if self.current_session_id:
            self.db.add_transcription(self.current_session_id, text, time.time())
    
    def generate_summary(self):
        """Generate a summary from the current transcription buffer"""
        if not self.summarization_service or not self.transcription_buffer.strip():
            return
        
        try:
            # Request summary asynchronously
            self.summarization_service.summarize_text(
                self.transcription_buffer,
                "Summarize the key points in 3-5 bullets.",
                self.on_summary
            )
            
            # Clear the transcription buffer for the next summary period
            self.transcription_buffer = ""
            
        except Exception as e:
            logger.error(f"Error generating summary: {str(e)}")
            self.status_bar.config(text=f"Error generating summary: {str(e)}")
    
    def on_summary(self, summary):
        """Handle new summary"""
        if not summary or not self.is_recording:
            return
        
        # Format the summary with timestamp
        timestamp = datetime.now().strftime("%H:%M:%S")
        formatted_summary = f"\n[{timestamp}]\n{summary}\n"
        
        # Update the UI
        self.summary_text.config(state=tk.NORMAL)
        self.summary_text.insert(tk.END, formatted_summary)
        self.summary_text.see(tk.END)
        self.summary_text.config(state=tk.DISABLED)
        
        # Save to database
        if self.current_session_id:
            self.db.add_summary(self.current_session_id, summary, time.time())
    
    def new_session(self):
        """Start a new session"""
        if self.is_recording:
            if messagebox.askyesno("Confirm", "Stop current recording and start a new session?"):
                self.stop_recording()
                self.start_recording()
        else:
            self.start_recording()
    
    def export_session(self):
        """Export the current session"""
        if not self.current_session_id:
            messagebox.showinfo("Info", "No active session to export")
            return
        
        try:
            # Get session data
            session_data = self.db.get_session_data(self.current_session_id)
            
            # Ask for save location and format
            filetypes = [("PDF files", "*.pdf"), ("Word files", "*.docx"), ("Text files", "*.txt")]
            filename = filedialog.asksaveasfilename(
                defaultextension=".pdf",
                filetypes=filetypes
            )
            
            if not filename:
                return
            
            # Export the file
            if self.export_service.export_session(filename, session_data):
                messagebox.showinfo("Success", f"Session exported to {filename}")
            else:
                messagebox.showerror("Error", "Failed to export session")
                
        except Exception as e:
            logger.error(f"Error exporting session: {str(e)}")
            messagebox.showerror("Error", f"Failed to export session: {str(e)}")
    
    def show_settings(self):
        """Show the settings dialog"""
        # Create a new top-level window
        settings_window = tk.Toplevel(self.root)
        settings_window.title("Settings")
        settings_window.geometry("500x400")
        settings_window.transient(self.root)  # Make it modal
        settings_window.grab_set()
        
        # Create a notebook for tabbed settings
        notebook = ttk.Notebook(settings_window)
        notebook.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # General settings tab
        general_frame = ttk.Frame(notebook, padding=10)
        notebook.add(general_frame, text="General")
        
        # Ollama settings tab
        ollama_frame = ttk.Frame(notebook, padding=10)
        notebook.add(ollama_frame, text="Ollama")
        
        # Audio settings tab
        audio_frame = ttk.Frame(notebook, padding=10)
        notebook.add(audio_frame, text="Audio")
        
        # General settings
        ttk.Label(general_frame, text="Summary Interval (seconds):").grid(row=0, column=0, sticky=tk.W, pady=5)
        summary_interval = tk.StringVar(value=str(self.config.get("summary_interval", 30)))
        ttk.Entry(general_frame, textvariable=summary_interval).grid(row=0, column=1, sticky=tk.W, pady=5)
        
        # Ollama settings
        ttk.Label(ollama_frame, text="Ollama Host:").grid(row=0, column=0, sticky=tk.W, pady=5)
        ollama_host = tk.StringVar(value=self.config.get("ollama_host", "http://localhost:11434"))
        ttk.Entry(ollama_frame, textvariable=ollama_host, width=40).grid(row=0, column=1, sticky=tk.W, pady=5)
        
        ttk.Label(ollama_frame, text="Transcription Model:").grid(row=1, column=0, sticky=tk.W, pady=5)
        transcription_model = tk.StringVar(value=self.config.get("transcription_model", "whisper"))
        ttk.Entry(ollama_frame, textvariable=transcription_model).grid(row=1, column=1, sticky=tk.W, pady=5)
        
        ttk.Label(ollama_frame, text="Summarization Model:").grid(row=2, column=0, sticky=tk.W, pady=5)
        summarization_model = tk.StringVar(value=self.config.get("summarization_model", "llama2"))
        ttk.Entry(ollama_frame, textvariable=summarization_model).grid(row=2, column=1, sticky=tk.W, pady=5)
        
        # Audio settings
        ttk.Label(audio_frame, text="Sample Rate:").grid(row=0, column=0, sticky=tk.W, pady=5)
        sample_rate = tk.StringVar(value=str(self.config.get("sample_rate", 16000)))
        ttk.Entry(audio_frame, textvariable=sample_rate).grid(row=0, column=1, sticky=tk.W, pady=5)
        
        ttk.Label(audio_frame, text="Channels:").grid(row=1, column=0, sticky=tk.W, pady=5)
        channels = tk.StringVar(value=str(self.config.get("channels", 1)))
        ttk.Entry(audio_frame, textvariable=channels).grid(row=1, column=1, sticky=tk.W, pady=5)
        
        ttk.Label(audio_frame, text="Chunk Size:").grid(row=2, column=0, sticky=tk.W, pady=5)
        chunk_size = tk.StringVar(value=str(self.config.get("chunk_size", 1024)))
        ttk.Entry(audio_frame, textvariable=chunk_size).grid(row=2, column=1, sticky=tk.W, pady=5)
        
        # Buttons
        def save_settings():
            try:
                # Update config
                self.config.set("summary_interval", int(summary_interval.get()))
                self.config.set("ollama_host", ollama_host.get())
                self.config.set("transcription_model", transcription_model.get())
                self.config.set("summarization_model", summarization_model.get())
                self.config.set("sample_rate", int(sample_rate.get()))
                self.config.set("channels", int(channels.get()))
                self.config.set("chunk_size", int(chunk_size.get()))
                
                # Save config
                self.config.save()
                
                messagebox.showinfo("Success", "Settings saved successfully")
                settings_window.destroy()
            except Exception as e:
                messagebox.showerror("Error", f"Failed to save settings: {str(e)}")
        
        button_frame = ttk.Frame(settings_window)
        button_frame.pack(fill=tk.X, padx=10, pady=10)
        
        ttk.Button(button_frame, text="Save", command=save_settings).pack(side=tk.RIGHT, padx=5)
        ttk.Button(button_frame, text="Cancel", command=settings_window.destroy).pack(side=tk.RIGHT, padx=5)
    
    def show_about(self):
        """Show the about dialog"""
        about_text = """SumOnTheFly v1.0
        
A desktop application that records audio, transcribes it in real-time, and generates bullet-point summaries.

Created with Python and Tkinter.
Uses Ollama for local AI processing.

Ollama Server: http://CarlsMacStudio.got.volvo.net:11434
Transcription Model: dimavz/whisper-tiny
Summarization Model: qwen3
        """
        messagebox.showinfo("About SumOnTheFly", about_text)

# Main entry point
def main():
    """Main entry point for the application"""
    try:
        # Create the root window
        root = tk.Tk()
        
        # Create and run the application
        app = SumOnTheFlyApp(root)
        
        # Handle window close
        def on_closing():
            if app.is_recording:
                if messagebox.askyesno("Confirm", "Recording is in progress. Stop recording and exit?"):
                    app.stop_recording()
                    root.destroy()
            else:
                root.destroy()
        
        root.protocol("WM_DELETE_WINDOW", on_closing)
        root.mainloop()
        
    except Exception as e:
        logger.critical(f"Unhandled exception: {str(e)}")
        messagebox.showerror("Critical Error", f"An unhandled error occurred: {str(e)}")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
