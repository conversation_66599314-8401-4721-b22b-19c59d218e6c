#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import logging
import time
import numpy as np
import os
import sys

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Check for audio libraries
try:
    import pyaudio
    PYAUDIO_AVAILABLE = True
    print("PyAudio is available")
except ImportError:
    PYAUDIO_AVAILABLE = False
    print("PyAudio is not available")

try:
    import wave
    WAVE_AVAILABLE = True
    print("wave module is available")
except ImportError:
    WAVE_AVAILABLE = False
    print("wave module is not available")

def simple_record_test():
    """Simple test to record audio and save to a file"""
    if not PYAUDIO_AVAILABLE:
        print("Cannot test recording: PyAudio not available")
        return False
    
    try:
        # Initialize PyAudio
        p = pyaudio.PyAudio()
        
        # List available devices
        print("\nAvailable audio devices:")
        device_count = p.get_device_count()
        for i in range(device_count):
            try:
                device_info = p.get_device_info_by_index(i)
                if device_info.get('maxInputChannels', 0) > 0:
                    print(f"  Device {i}: {device_info['name']} (Inputs: {device_info['maxInputChannels']})")
            except Exception as e:
                print(f"  Error getting info for device {i}: {str(e)}")
        
        # Try to get default input device
        try:
            default_input = p.get_default_input_device_info()
            print(f"\nDefault input device: {default_input['name']} (index: {default_input['index']})")
            device_index = default_input['index']
        except Exception as e:
            print(f"Error getting default input device: {str(e)}")
            # Try to find any input device
            device_index = None
            for i in range(device_count):
                try:
                    device_info = p.get_device_info_by_index(i)
                    if device_info.get('maxInputChannels', 0) > 0:
                        device_index = i
                        print(f"Using first available input device: {device_info['name']} (index: {i})")
                        break
                except Exception:
                    continue
        
        if device_index is None:
            print("No input devices found. Cannot test recording.")
            p.terminate()
            return False
        
        # Record audio
        print("\nRecording 3 seconds of audio...")
        
        # Open stream
        stream = p.open(
            format=pyaudio.paInt16,
            channels=1,
            rate=16000,
            input=True,
            input_device_index=device_index,
            frames_per_buffer=1024
        )
        
        frames = []
        for i in range(0, int(16000 / 1024 * 3)):
            data = stream.read(1024, exception_on_overflow=False)
            frames.append(data)
            # Print audio levels to see if we're capturing anything
            audio_array = np.frombuffer(data, dtype=np.int16)
            level = np.abs(audio_array).mean()
            print(f"Audio level: {level:.2f}")
        
        print("Finished recording")
        
        # Stop and close the stream
        stream.stop_stream()
        stream.close()
        
        # Save to file if wave is available
        if WAVE_AVAILABLE:
            filename = "test_recording.wav"
            print(f"\nSaving recording to {filename}")
            wf = wave.open(filename, 'wb')
            wf.setnchannels(1)
            wf.setsampwidth(p.get_sample_size(pyaudio.paInt16))
            wf.setframerate(16000)
            wf.writeframes(b''.join(frames))
            wf.close()
            print(f"Saved recording to {os.path.abspath(filename)}")
        
        # Clean up
        p.terminate()
        return True
    except Exception as e:
        print(f"Error in recording test: {str(e)}")
        return False

def generate_test_audio():
    """Generate test audio and save to a file"""
    try:
        print("\nGenerating test audio...")
        
        # Generate a simple sine wave
        sample_rate = 16000
        duration = 3  # seconds
        frequency = 440  # 440 Hz = A4 note
        
        t = np.linspace(0, duration, int(sample_rate * duration), False)
        audio_array = np.sin(2 * np.pi * frequency * t) * 32767 / 4  # Quarter amplitude
        audio_array = audio_array.astype(np.int16)
        
        # Save to file if wave is available
        if WAVE_AVAILABLE:
            filename = "test_synthetic.wav"
            print(f"Saving synthetic audio to {filename}")
            wf = wave.open(filename, 'wb')
            wf.setnchannels(1)
            wf.setsampwidth(2)  # 16-bit audio = 2 bytes
            wf.setframerate(sample_rate)
            wf.writeframes(audio_array.tobytes())
            wf.close()
            print(f"Saved synthetic audio to {os.path.abspath(filename)}")
        
        return True
    except Exception as e:
        print(f"Error generating test audio: {str(e)}")
        return False

if __name__ == "__main__":
    print("===== SIMPLE AUDIO TEST =====\n")
    
    # Try to record audio
    simple_record_test()
    
    # Generate test audio
    generate_test_audio()
    
    print("\n===== TEST COMPLETED =====")
    input("Press Enter to exit...")
