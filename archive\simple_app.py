#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import threading
import time
import logging
import os
import sys
import json
from datetime import datetime
import numpy as np

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("sumonthefly.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# Simplified versions of backend services with minimal dependencies
class SimpleAudioCapture:
    """Simplified audio capture that generates test data"""
    
    def __init__(self, on_audio_chunk=None):
        self.on_audio_chunk = on_audio_chunk
        self.is_recording = False
        self.thread = None
        self.sample_rate = 16000
        self.chunk_size = 1024
    
    def start_recording(self):
        if self.is_recording:
            return False
        
        self.is_recording = True
        self.thread = threading.Thread(target=self._generate_audio, daemon=True)
        self.thread.start()
        logger.info("Started generating test audio")
        return True
    
    def stop_recording(self):
        self.is_recording = False
        if self.thread and self.thread.is_alive():
            self.thread.join(timeout=2.0)
        logger.info("Stopped audio generation")
        return True
    
    def _generate_audio(self):
        """Generate synthetic audio data"""
        frequency = 440  # 440 Hz = A4 note
        sample_index = 0
        
        while self.is_recording:
            # Generate chunk_size samples of a sine wave
            t = np.arange(sample_index, sample_index + self.chunk_size) / self.sample_rate
            audio_array = np.sin(2 * np.pi * frequency * t) * 32767 / 4  # Quarter amplitude
            audio_array = audio_array.astype(np.int16)
            
            if self.on_audio_chunk:
                self.on_audio_chunk(audio_array)
            
            sample_index += self.chunk_size
            time.sleep(self.chunk_size / self.sample_rate)  # Sleep for the duration of the chunk

class SimpleTranscriptionService:
    """Simplified transcription service that generates test transcriptions"""
    
    def __init__(self, ollama_host="http://localhost:11434", model="whisper", on_transcription=None):
        self.ollama_host = ollama_host
        self.model = model
        self.on_transcription = on_transcription
        self.is_processing = False
        self.buffer = []
        self.buffer_lock = threading.Lock()
        self.processing_thread = None
        self.last_processed_time = 0
        self.phrases = [
            "This is a test transcription.",
            "The application is running in simplified mode.",
            "Audio is being processed locally.",
            "This is simulated transcription data.",
            "SumOnTheFly is running without external dependencies.",
            "This demo shows the core functionality.",
            "Real transcription requires Ollama with Whisper model."
        ]
    
    def process_audio(self, audio_chunk):
        with self.buffer_lock:
            self.buffer.append(audio_chunk)
        
        current_time = time.time()
        if (not self.is_processing and 
            current_time - self.last_processed_time >= 2.0):  # Process every 2 seconds
            self._start_processing()
    
    def _start_processing(self):
        if self.is_processing:
            return
        
        self.is_processing = True
        self.processing_thread = threading.Thread(target=self._process_buffer, daemon=True)
        self.processing_thread.start()
    
    def _process_buffer(self):
        try:
            # Clear the buffer
            with self.buffer_lock:
                self.buffer = []
            
            # Generate a simulated transcription
            phrase_index = int(time.time()) % len(self.phrases)
            transcription = self.phrases[phrase_index]
            
            # Sleep to simulate processing time
            time.sleep(0.5)
            
            # Call the callback with the transcription result
            if self.on_transcription:
                self.on_transcription(transcription)
                
        except Exception as e:
            logger.error(f"Error in transcription: {str(e)}")
        finally:
            self.last_processed_time = time.time()
            self.is_processing = False

class SimpleSummarizationService:
    """Simplified summarization service that generates test summaries"""
    
    def __init__(self, ollama_host="http://localhost:11434", model="llama2", on_summary=None):
        self.ollama_host = ollama_host
        self.model = model
        self.on_summary = on_summary
        self.summaries = [
            "- This is a simulated summary\n- Running in simplified mode\n- No external dependencies required",
            "- Test bullet point summary\n- Shows core application functionality\n- Demonstrates the user interface",
            "- Simplified version of SumOnTheFly\n- Real summarization requires Ollama\n- Using qwen3 model for production use"
        ]
    
    def summarize_text(self, text, prompt_template=""):
        """Generate a simulated summary"""
        thread = threading.Thread(target=self._generate_summary, daemon=True)
        thread.start()
    
    def _generate_summary(self):
        try:
            # Select a random summary
            summary_index = int(time.time() / 10) % len(self.summaries)
            summary = self.summaries[summary_index]
            
            # Sleep to simulate processing time
            time.sleep(1.0)
            
            # Call the callback with the summary
            if self.on_summary:
                self.on_summary(summary)
                
        except Exception as e:
            logger.error(f"Error generating summary: {str(e)}")

class SimpleExportService:
    """Simplified export service that saves to text files only"""
    
    def export_session(self, filename, session_data):
        try:
            # Force .txt extension
            filename = os.path.splitext(filename)[0] + ".txt"
            
            # Extract session information
            session = session_data.get('session', {})
            transcriptions = session_data.get('transcriptions', [])
            summaries = session_data.get('summaries', [])
            
            with open(filename, 'w', encoding='utf-8') as f:
                # Write title
                title = f"SumOnTheFly Session: {session.get('name', 'Unnamed Session')}"
                f.write(f"{title}\n{'=' * len(title)}\n\n")
                
                # Write session information
                start_time = datetime.fromtimestamp(session.get('start_time', 0))
                end_time = datetime.fromtimestamp(session.get('end_time', 0)) if session.get('end_time') else None
                duration = "N/A"
                if end_time:
                    duration_seconds = (end_time - start_time).total_seconds()
                    hours, remainder = divmod(int(duration_seconds), 3600)
                    minutes, seconds = divmod(remainder, 60)
                    duration = f"{hours:02d}:{minutes:02d}:{seconds:02d}"
                
                f.write(f"Start Time: {start_time.strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write(f"End Time: {end_time.strftime('%Y-%m-%d %H:%M:%S') if end_time else 'N/A'}\n")
                f.write(f"Duration: {duration}\n\n")
                
                # Write summaries
                if summaries:
                    f.write("SUMMARIES\n=========\n\n")
                    
                    for i, summary in enumerate(summaries):
                        timestamp = datetime.fromtimestamp(summary.get('timestamp', 0))
                        f.write(f"Summary {i+1} - {timestamp.strftime('%H:%M:%S')}\n")
                        f.write(f"{'-' * 30}\n")
                        f.write(f"{summary.get('text', '')}\n\n")
                
                # Write transcriptions
                if transcriptions:
                    f.write("TRANSCRIPTIONS\n==============\n\n")
                    
                    for i, trans in enumerate(transcriptions):
                        timestamp = datetime.fromtimestamp(trans.get('timestamp', 0))
                        f.write(f"[{timestamp.strftime('%H:%M:%S')}] {trans.get('text', '')}\n")
            
            logger.info(f"Exported session to: {filename}")
            return True
            
        except Exception as e:
            logger.error(f"Error exporting session: {str(e)}")
            return False

class SimpleDatabase:
    """Simplified in-memory database"""
    
    def __init__(self):
        self.sessions = {}
        self.transcriptions = {}
        self.summaries = {}
        self.next_session_id = 1
        self.next_transcription_id = 1
        self.next_summary_id = 1
    
    def create_session(self, name, metadata=None):
        session_id = self.next_session_id
        self.next_session_id += 1
        
        self.sessions[session_id] = {
            'id': session_id,
            'name': name,
            'start_time': time.time(),
            'end_time': None,
            'metadata': metadata or {}
        }
        
        self.transcriptions[session_id] = []
        self.summaries[session_id] = []
        
        return session_id
    
    def update_session_end_time(self, session_id):
        if session_id in self.sessions:
            self.sessions[session_id]['end_time'] = time.time()
            return True
        return False
    
    def add_transcription(self, session_id, text, timestamp):
        if session_id in self.transcriptions:
            transcription_id = self.next_transcription_id
            self.next_transcription_id += 1
            
            self.transcriptions[session_id].append({
                'id': transcription_id,
                'session_id': session_id,
                'text': text,
                'timestamp': timestamp
            })
            
            return transcription_id
        return -1
    
    def add_summary(self, session_id, text, timestamp):
        if session_id in self.summaries:
            summary_id = self.next_summary_id
            self.next_summary_id += 1
            
            self.summaries[session_id].append({
                'id': summary_id,
                'session_id': session_id,
                'text': text,
                'timestamp': timestamp
            })
            
            return summary_id
        return -1
    
    def get_session_data(self, session_id):
        if session_id not in self.sessions:
            return {}
        
        return {
            'session': self.sessions[session_id],
            'transcriptions': self.transcriptions[session_id],
            'summaries': self.summaries[session_id]
        }

class SimpleConfig:
    """Simplified configuration manager"""
    
    def __init__(self):
        self.config = {
            # General settings
            "summary_interval": 30,  # seconds
            "language": "English",
            "auto_start": False,
            
            # Ollama settings
            "ollama_host": "http://CarlsMacStudio.got.volvo.net:11434",
            "transcription_model": "dimavz/whisper-tiny",
            "summarization_model": "qwen3",
            
            # Audio settings
            "sample_rate": 16000,
            "channels": 1,
            "chunk_size": 1024
        }
    
    def get(self, key, default=None):
        return self.config.get(key, default)
    
    def set(self, key, value):
        self.config[key] = value

class SimpleSumOnTheFlyApp:
    """Simplified SumOnTheFly application with minimal dependencies"""
    
    def __init__(self, root):
        self.root = root
        self.root.title("SumOnTheFly (Simplified Version)")
        self.root.geometry("1024x768")
        self.root.minsize(800, 600)
        
        # Initialize components with simplified versions
        self.config = SimpleConfig()
        self.db = SimpleDatabase()
        self.audio_capture = None
        self.transcription_service = None
        self.summarization_service = None
        self.export_service = SimpleExportService()
        
        # State variables
        self.is_recording = False
        self.current_session_id = None
        self.transcription_buffer = ""
        self.last_summary_time = 0
        self.recording_start_time = 0
        
        # Set up UI
        self.setup_ui()
        
        # Start UI update timer
        self.update_ui()
        
        # Show startup message
        messagebox.showinfo(
            "Simplified Mode", 
            "Running in simplified mode with simulated audio and AI responses.\n\n"
            "This version does not require external dependencies and works offline."
        )
    
    def setup_ui(self):
        """Set up the main UI components"""
        # Main frame
        self.main_frame = ttk.Frame(self.root, padding=10)
        self.main_frame.pack(fill=tk.BOTH, expand=True)
        
        # Create menu
        self.create_menu()
        
        # Control panel
        control_frame = ttk.LabelFrame(self.main_frame, text="Controls")
        control_frame.pack(fill=tk.X, pady=5)
        
        self.record_button = ttk.Button(
            control_frame, 
            text="Start Recording", 
            command=self.toggle_recording
        )
        self.record_button.pack(side=tk.LEFT, padx=5, pady=5)
        
        self.session_label = ttk.Label(control_frame, text="No active session")
        self.session_label.pack(side=tk.LEFT, padx=5, pady=5)
        
        self.time_label = ttk.Label(control_frame, text="00:00:00")
        self.time_label.pack(side=tk.RIGHT, padx=5, pady=5)
        
        # Transcription panel
        transcription_frame = ttk.LabelFrame(self.main_frame, text="Real-time Transcription")
        transcription_frame.pack(fill=tk.BOTH, expand=True, pady=5)
        
        self.transcription_text = tk.Text(
            transcription_frame,
            wrap=tk.WORD,
            state=tk.DISABLED
        )
        transcription_scroll = ttk.Scrollbar(
            transcription_frame, 
            command=self.transcription_text.yview
        )
        self.transcription_text.configure(yscrollcommand=transcription_scroll.set)
        
        self.transcription_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        transcription_scroll.pack(side=tk.RIGHT, fill=tk.Y)
        
        # Summary panel
        summary_frame = ttk.LabelFrame(self.main_frame, text="Summaries")
        summary_frame.pack(fill=tk.BOTH, expand=True, pady=5)
        
        self.summary_text = tk.Text(
            summary_frame,
            wrap=tk.WORD,
            state=tk.DISABLED,
            height=10
        )
        summary_scroll = ttk.Scrollbar(
            summary_frame, 
            command=self.summary_text.yview
        )
        self.summary_text.configure(yscrollcommand=summary_scroll.set)
        
        self.summary_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        summary_scroll.pack(side=tk.RIGHT, fill=tk.Y)
        
        # Status bar
        self.status_bar = ttk.Label(
            self.root, 
            text="Ready (Simplified Mode)", 
            relief=tk.SUNKEN, 
            anchor=tk.W
        )
        self.status_bar.pack(side=tk.BOTTOM, fill=tk.X)
    
    def create_menu(self):
        """Create the application menu"""
        menubar = tk.Menu(self.root)
        
        # File menu
        file_menu = tk.Menu(menubar, tearoff=0)
        file_menu.add_command(label="New Session", command=self.new_session)
        file_menu.add_separator()
        file_menu.add_command(label="Export Session", command=self.export_session)
        file_menu.add_separator()
        file_menu.add_command(label="Exit", command=self.root.quit)
        menubar.add_cascade(label="File", menu=file_menu)
        
        # Help menu
        help_menu = tk.Menu(menubar, tearoff=0)
        help_menu.add_command(label="About", command=self.show_about)
        menubar.add_cascade(label="Help", menu=help_menu)
        
        self.root.config(menu=menubar)
    
    def update_ui(self):
        """Periodically update the UI"""
        if self.is_recording:
            # Update recording time
            elapsed = time.time() - self.recording_start_time
            hours, remainder = divmod(int(elapsed), 3600)
            minutes, seconds = divmod(remainder, 60)
            self.time_label.config(text=f"{hours:02d}:{minutes:02d}:{seconds:02d}")
            
            # Check if it's time for a new summary
            summary_interval = self.config.get("summary_interval", 30)
            if elapsed - self.last_summary_time >= summary_interval and self.transcription_buffer.strip():
                self.generate_summary()
                self.last_summary_time = elapsed
        
        # Schedule the next update
        self.root.after(1000, self.update_ui)
    
    def toggle_recording(self):
        """Start or stop recording"""
        if not self.is_recording:
            self.start_recording()
        else:
            self.stop_recording()
    
    def start_recording(self):
        """Start a new recording session"""
        try:
            # Create a new session
            session_name = f"Session_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            self.current_session_id = self.db.create_session(session_name)
            
            # Initialize services
            self.audio_capture = SimpleAudioCapture(
                on_audio_chunk=self.on_audio_chunk
            )
            
            self.transcription_service = SimpleTranscriptionService(
                ollama_host=self.config.get("ollama_host"),
                model=self.config.get("transcription_model"),
                on_transcription=self.on_transcription
            )
            
            self.summarization_service = SimpleSummarizationService(
                ollama_host=self.config.get("ollama_host"),
                model=self.config.get("summarization_model"),
                on_summary=self.on_summary
            )
            
            # Start recording
            success = self.audio_capture.start_recording()
            if not success:
                raise Exception("Failed to start audio recording")
            
            # Update UI
            self.is_recording = True
            self.recording_start_time = time.time()
            self.last_summary_time = 0
            self.transcription_buffer = ""
            self.record_button.config(text="Stop Recording")
            self.session_label.config(text=f"Active session: {session_name}")
            self.status_bar.config(text="Recording... (Simplified Mode)")
            
            # Clear text areas
            self.transcription_text.config(state=tk.NORMAL)
            self.transcription_text.delete(1.0, tk.END)
            self.transcription_text.config(state=tk.DISABLED)
            
            self.summary_text.config(state=tk.NORMAL)
            self.summary_text.delete(1.0, tk.END)
            self.summary_text.config(state=tk.DISABLED)
            
            logger.info(f"Started recording session: {session_name}")
            
        except Exception as e:
            logger.error(f"Error starting recording: {str(e)}")
            messagebox.showerror("Error", f"Failed to start recording: {str(e)}")
            self.cleanup_services()
    
    def stop_recording(self):
        """Stop the current recording session"""
        if not self.is_recording:
            return
        
        try:
            # Stop services
            if self.audio_capture:
                self.audio_capture.stop_recording()
            
            # Generate final summary if needed
            if self.transcription_buffer.strip():
                self.generate_summary()
            
            # Update UI
            self.is_recording = False
            self.record_button.config(text="Start Recording")
            self.status_bar.config(text="Ready (Simplified Mode)")
            
            # Save session data
            if self.current_session_id:
                self.db.update_session_end_time(self.current_session_id)
            
            logger.info("Stopped recording session")
            
        except Exception as e:
            logger.error(f"Error stopping recording: {str(e)}")
            messagebox.showerror("Error", f"Error stopping recording: {str(e)}")
        finally:
            self.cleanup_services()
    
    def cleanup_services(self):
        """Clean up and release resources"""
        self.audio_capture = None
        self.transcription_service = None
        self.summarization_service = None
    
    def on_audio_chunk(self, audio_chunk):
        """Handle new audio chunk from the recorder"""
        if self.transcription_service:
            self.transcription_service.process_audio(audio_chunk)
    
    def on_transcription(self, text):
        """Handle new transcription text"""
        if not text or not self.is_recording:
            return
        
        # Update the transcription buffer
        self.transcription_buffer += text + " "
        
        # Update the UI
        self.transcription_text.config(state=tk.NORMAL)
        self.transcription_text.insert(tk.END, text + " ")
        self.transcription_text.see(tk.END)
        self.transcription_text.config(state=tk.DISABLED)
        
        # Save to database
        if self.current_session_id:
            self.db.add_transcription(self.current_session_id, text, time.time())
    
    def generate_summary(self):
        """Generate a summary from the current transcription buffer"""
        if not self.summarization_service or not self.transcription_buffer.strip():
            return
        
        try:
            # Request summary asynchronously
            self.summarization_service.summarize_text(
                self.transcription_buffer,
                "Summarize the key points in 3-5 bullets."
            )
            
            # Clear the transcription buffer for the next summary period
            self.transcription_buffer = ""
            
        except Exception as e:
            logger.error(f"Error generating summary: {str(e)}")
            self.status_bar.config(text=f"Error generating summary: {str(e)}")
    
    def on_summary(self, summary):
        """Handle new summary"""
        if not summary or not self.is_recording:
            return
        
        # Format the summary with timestamp
        timestamp = datetime.now().strftime("%H:%M:%S")
        formatted_summary = f"\n[{timestamp}]\n{summary}\n"
        
        # Update the UI
        self.summary_text.config(state=tk.NORMAL)
        self.summary_text.insert(tk.END, formatted_summary)
        self.summary_text.see(tk.END)
        self.summary_text.config(state=tk.DISABLED)
        
        # Save to database
        if self.current_session_id:
            self.db.add_summary(self.current_session_id, summary, time.time())
    
    def new_session(self):
        """Start a new session"""
        if self.is_recording:
            if messagebox.askyesno("Confirm", "Stop current recording and start a new session?"):
                self.stop_recording()
                self.start_recording()
        else:
            self.start_recording()
    
    def export_session(self):
        """Export the current session"""
        if not self.current_session_id:
            messagebox.showinfo("Info", "No active session to export")
            return
        
        try:
            # Get session data
            session_data = self.db.get_session_data(self.current_session_id)
            
            # Ask for save location
            filename = filedialog.asksaveasfilename(
                defaultextension=".txt",
                filetypes=[("Text files", "*.txt")]
            )
            
            if not filename:
                return
            
            # Export the file
            if self.export_service.export_session(filename, session_data):
                messagebox.showinfo("Success", f"Session exported to {filename}")
            else:
                messagebox.showerror("Error", "Failed to export session")
                
        except Exception as e:
            logger.error(f"Error exporting session: {str(e)}")
            messagebox.showerror("Error", f"Failed to export session: {str(e)}")
    
    def show_about(self):
        """Show the about dialog"""
        about_text = """SumOnTheFly v1.0 (Simplified Version)
        
A desktop application that simulates recording audio, transcribing it in real-time, and generating bullet-point summaries.

This simplified version works without external dependencies.
For full functionality with real audio capture and AI processing, use the full version with Ollama.

Ollama Server: http://CarlsMacStudio.got.volvo.net:11434
Transcription Model: dimavz/whisper-tiny
Summarization Model: qwen3
        """
        messagebox.showinfo("About SumOnTheFly", about_text)

def main():
    """Main entry point for the application"""
    try:
        # Create the root window
        root = tk.Tk()
        
        # Create and run the application
        app = SimpleSumOnTheFlyApp(root)
        root.mainloop()
        
    except Exception as e:
        logger.critical(f"Unhandled exception: {str(e)}")
        messagebox.showerror("Critical Error", f"An unhandled error occurred: {str(e)}")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
