import threading
import queue
import sounddevice as sd
import numpy as np
import whisper
import tkinter as tk
from tkinter import scrolledtext
import tempfile
import wave
import os
import sys
import subprocess

# Check if FFmpeg is available
def is_ffmpeg_available():
    try:
        subprocess.run(["ffmpeg", "-version"], stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        return True
    except FileNotFoundError:
        return False

class TranscriberApp:
    def __init__(self, root):
        self.root = root
        root.title("Live Whisper-transkribering")

        # Textfält för transkript
        self.text = scrolledtext.ScrolledText(root, wrap=tk.WORD, width=60, height=20)
        self.text.pack(padx=10, pady=10)

        # Start/Stop-knappar
        frame = tk.Frame(root)
        frame.pack(pady=5)
        self.btn_start = tk.Button(frame, text="Starta inspelning", command=self.start_recording)
        self.btn_start.pack(side=tk.LEFT, padx=5)
        self.btn_stop  = tk.Button(frame, text="Stoppa inspelning", command=self.stop_recording, state=tk.DISABLED)
        self.btn_stop.pack(side=tk.LEFT, padx=5)

        # Queue för ljuddata
        self.q = queue.Queue()
        self.recording = False

        # Check for FFmpeg
        if not is_ffmpeg_available():
            self.text.insert(tk.END, "⚠️ VARNING: FFmpeg hittades inte. Installera FFmpeg för att använda Whisper.\n")
            self.text.insert(tk.END, "Ladda ner från: https://ffmpeg.org/download.html\n")
            self.text.see(tk.END)
            self.btn_start.config(state=tk.DISABLED)
        else:
            # Ladda Whisper-modell (tiny för snabbhet)
            threading.Thread(target=self.load_model, daemon=True).start()

    def load_model(self):
        try:
            self.text.insert(tk.END, "Laddar Whisper-modell (tiny)...\n")
            self.text.see(tk.END)
            self.model = whisper.load_model("tiny")
            self.text.insert(tk.END, "Modellen laddad.\n")
            self.text.see(tk.END)
        except Exception as e:
            self.text.insert(tk.END, f"Fel vid laddning av modell: {str(e)}\n")
            self.text.see(tk.END)
            self.btn_start.config(state=tk.DISABLED)

    def audio_callback(self, indata, frames, time, status):
        """Körs i ljud‐callback, lägger rådata i q."""
        if status:
            print(status, flush=True)
        self.q.put(indata.copy())

    def start_recording(self):
        self.recording = True
        self.btn_start.config(state=tk.DISABLED)
        self.btn_stop.config(state=tk.NORMAL)
        self.text.insert(tk.END, "🌐 Inspelning pågår...\n")
        self.text.see(tk.END)
        # Starta stream i bakgrundstråd
        threading.Thread(target=self._record, daemon=True).start()

    def _record(self):
        # 16 kHz mono
        with sd.InputStream(samplerate=16000, channels=1, callback=self.audio_callback):
            while self.recording:
                sd.sleep(100)

    def stop_recording(self):
        self.recording = False
        self.btn_start.config(state=tk.NORMAL)
        self.btn_stop.config(state=tk.DISABLED)
        self.text.insert(tk.END, "⏹️ Inspelning stoppad. Transkriberar...\n")
        self.text.see(tk.END)
        # Extrahera allt ljud från kön och skriv till WAV
        threading.Thread(target=self.transcribe_buffer, daemon=True).start()

    def transcribe_buffer(self):
        # Samla ihop all data
        frames = []
        while not self.q.empty():
            frames.append(self.q.get())
        if not frames:
            self.text.insert(tk.END, "⚠️ Inget ljud inspelat!\n")
            return

        audio = np.concatenate(frames, axis=0)
        # Spara temporär WAV
        wav_path = None
        try:
            with tempfile.NamedTemporaryFile(suffix=".wav", delete=False) as tmp:
                wav_path = tmp.name
                wf = wave.open(wav_path, "wb")
                wf.setnchannels(1)
                wf.setsampwidth(2)  # 16 bit = 2 bytes
                wf.setframerate(16000)
                # Konvertera float32 → int16
                int_data = (audio * 32767).astype(np.int16)
                wf.writeframes(int_data.tobytes())
                wf.close()

            # Gör själva transkriptionen
            self.text.insert(tk.END, "Transkriberar ljudet...\n")
            self.text.see(tk.END)
            result = self.model.transcribe(wav_path, fp16=False)
            text = result["text"].strip()

            # Visa i GUI:t
            self.text.insert(tk.END, f"📝 Transkript: {text}\n\n")
            self.text.see(tk.END)
        except Exception as e:
            self.text.insert(tk.END, f"Fel vid transkription: {str(e)}\n")
            self.text.see(tk.END)
        finally:
            # Ta bort temporär fil
            if wav_path and os.path.exists(wav_path):
                try:
                    os.unlink(wav_path)
                except:
                    pass

if __name__ == "__main__":
    root = tk.Tk()
    app = TranscriberApp(root)
    root.mainloop()
