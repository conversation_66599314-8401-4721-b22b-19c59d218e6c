#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import pyaudio
import wave
import numpy as np
import time
import os
import threading
from datetime import datetime
import logging
import sys

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("robust_recorder.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class RobustRecorder:
    def __init__(self, root):
        self.root = root
        self.root.title("Robust Audio Recorder")
        self.root.geometry("500x300")
        
        # Audio parameters
        self.chunk = 1024
        self.format = pyaudio.paInt16
        self.channels = 1
        self.rate = 16000
        
        # State variables
        self.frames = []
        self.is_recording = False
        self.p = None
        self.stream = None
        self.audio_level = 0
        self.recording_thread = None
        
        # Create UI
        self.create_widgets()
        
        # Detect audio devices
        self.detect_audio_devices()
        
        # Start UI update timer
        self.update_ui()
    
    def create_widgets(self):
        # Main frame
        main_frame = ttk.Frame(self.root, padding=10)
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # Device selection
        device_frame = ttk.LabelFrame(main_frame, text="Audio Device")
        device_frame.pack(fill=tk.X, pady=5)
        
        self.device_var = tk.StringVar()
        self.device_dropdown = ttk.Combobox(device_frame, textvariable=self.device_var, state="readonly")
        self.device_dropdown.pack(side=tk.LEFT, padx=5, pady=5, fill=tk.X, expand=True)
        self.device_dropdown.bind("<<ComboboxSelected>>", self.on_device_selected)
        
        refresh_button = ttk.Button(device_frame, text="Refresh", command=self.detect_audio_devices)
        refresh_button.pack(side=tk.RIGHT, padx=5, pady=5)
        
        # Buttons frame
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X, pady=10)
        
        self.record_button = ttk.Button(
            button_frame, 
            text="Start Recording", 
            command=self.toggle_recording
        )
        self.record_button.pack(side=tk.LEFT, padx=5)
        
        self.save_button = ttk.Button(
            button_frame, 
            text="Save Recording", 
            command=self.save_recording, 
            state=tk.DISABLED
        )
        self.save_button.pack(side=tk.LEFT, padx=5)
        
        # Status and time frame
        info_frame = ttk.Frame(main_frame)
        info_frame.pack(fill=tk.X, pady=10)
        
        self.status_label = ttk.Label(info_frame, text="Ready")
        self.status_label.pack(side=tk.LEFT, padx=5)
        
        self.time_label = ttk.Label(info_frame, text="00:00")
        self.time_label.pack(side=tk.RIGHT, padx=5)
        
        # Audio level visualization
        level_frame = ttk.LabelFrame(main_frame, text="Audio Level")
        level_frame.pack(fill=tk.X, pady=10)
        
        self.level_canvas = tk.Canvas(level_frame, height=30, bg="white")
        self.level_canvas.pack(fill=tk.X, padx=5, pady=5)
    
    def detect_audio_devices(self):
        """Detect available audio devices"""
        try:
            p = pyaudio.PyAudio()
            device_count = p.get_device_count()
            devices = []
            self.device_indices = []
            
            # Get all input devices
            for i in range(device_count):
                try:
                    device_info = p.get_device_info_by_index(i)
                    if device_info.get('maxInputChannels', 0) > 0:
                        devices.append(f"{device_info.get('name')}")
                        self.device_indices.append(i)
                except Exception as e:
                    logger.warning(f"Error getting info for device {i}: {str(e)}")
            
            # Update dropdown
            self.device_dropdown['values'] = devices
            if devices:
                self.device_dropdown.current(0)
                self.status_label.config(text=f"Found {len(devices)} audio input devices")
            else:
                self.status_label.config(text="No audio input devices found")
            
            # Clean up
            p.terminate()
            
        except Exception as e:
            logger.error(f"Error detecting audio devices: {str(e)}")
            self.status_label.config(text=f"Error: {str(e)}")
    
    def on_device_selected(self, event):
        """Handle device selection"""
        if self.is_recording:
            messagebox.showwarning("Warning", "Please stop recording before changing the device")
            return
    
    def update_ui(self):
        """Periodically update the UI"""
        if self.is_recording:
            # Update recording time
            elapsed = time.time() - self.start_time
            mins, secs = divmod(int(elapsed), 60)
            self.time_label.config(text=f"{mins:02d}:{secs:02d}")
            
            # Update audio level visualization
            self.update_level_visualization(self.audio_level)
        
        # Schedule next update
        self.root.after(100, self.update_ui)
    
    def update_level_visualization(self, level):
        """Update the audio level visualization"""
        self.level_canvas.delete("all")
        width = self.level_canvas.winfo_width()
        height = self.level_canvas.winfo_height()
        
        # Draw background
        self.level_canvas.create_rectangle(0, 0, width, height, fill="white")
        
        # Draw level bar
        bar_width = int(width * (level / 100))
        
        # Color based on level
        if level < 30:
            color = "green"
        elif level < 70:
            color = "yellow"
        else:
            color = "red"
        
        self.level_canvas.create_rectangle(0, 0, bar_width, height, fill=color)
    
    def toggle_recording(self):
        """Start or stop recording"""
        if not self.is_recording:
            self.start_recording()
        else:
            self.stop_recording()
    
    def start_recording(self):
        """Start recording audio"""
        try:
            # Get selected device index
            selected_index = self.device_dropdown.current()
            if selected_index < 0 or selected_index >= len(self.device_indices):
                messagebox.showerror("Error", "No audio device selected")
                return
            
            device_index = self.device_indices[selected_index]
            
            # Initialize PyAudio
            self.p = pyaudio.PyAudio()
            
            # Clear previous recording
            self.frames = []
            
            # Open stream
            self.stream = self.p.open(
                format=self.format,
                channels=self.channels,
                rate=self.rate,
                input=True,
                input_device_index=device_index,
                frames_per_buffer=self.chunk
            )
            
            # Update state and UI
            self.is_recording = True
            self.start_time = time.time()
            self.record_button.config(text="Stop Recording")
            self.save_button.config(state=tk.DISABLED)
            self.status_label.config(text="Recording...")
            
            # Start recording thread
            self.recording_thread = threading.Thread(target=self._record_audio)
            self.recording_thread.daemon = True
            self.recording_thread.start()
            
            logger.info(f"Started recording with device index {device_index}")
            
        except Exception as e:
            logger.error(f"Error starting recording: {e}")
            messagebox.showerror("Error", f"Could not start recording: {str(e)}")
            self.cleanup()
    
    def _record_audio(self):
        """Record audio in a separate thread"""
        try:
            while self.is_recording and self.stream:
                try:
                    # Read audio data
                    data = self.stream.read(self.chunk, exception_on_overflow=False)
                    self.frames.append(data)
                    
                    # Calculate audio level
                    audio_array = np.frombuffer(data, dtype=np.int16)
                    level = np.abs(audio_array).mean()
                    normalized_level = min(100, level / 50)  # Normalize to 0-100
                    self.audio_level = normalized_level
                    
                except Exception as e:
                    logger.error(f"Error reading audio: {str(e)}")
                    time.sleep(0.1)  # Avoid tight loop on errors
        except Exception as e:
            logger.error(f"Error in recording thread: {str(e)}")
        finally:
            logger.info("Recording thread finished")
    
    def stop_recording(self):
        """Stop recording audio"""
        if not self.is_recording:
            return
        
        try:
            # Signal recording thread to stop
            self.is_recording = False
            
            # Wait for recording thread to finish (with timeout)
            if self.recording_thread and self.recording_thread.is_alive():
                self.recording_thread.join(timeout=1.0)
            
            # Cleanup in a separate thread to avoid UI freezing
            cleanup_thread = threading.Thread(target=self._cleanup_async)
            cleanup_thread.daemon = True
            cleanup_thread.start()
            
            # Update UI immediately
            self.record_button.config(text="Start Recording")
            self.save_button.config(state=tk.NORMAL if self.frames else tk.DISABLED)
            self.status_label.config(text="Recording stopped")
            
            logger.info("Stopped recording")
            
        except Exception as e:
            logger.error(f"Error stopping recording: {e}")
            messagebox.showerror("Error", f"Error stopping recording: {str(e)}")
    
    def _cleanup_async(self):
        """Clean up resources in a separate thread"""
        try:
            # Close stream
            if self.stream:
                try:
                    self.stream.stop_stream()
                    self.stream.close()
                except Exception as e:
                    logger.error(f"Error closing stream: {e}")
                finally:
                    self.stream = None
            
            # Terminate PyAudio
            if self.p:
                try:
                    self.p.terminate()
                except Exception as e:
                    logger.error(f"Error terminating PyAudio: {e}")
                finally:
                    self.p = None
            
            logger.info("Cleanup completed successfully")
            
        except Exception as e:
            logger.error(f"Error in cleanup: {e}")
    
    def cleanup(self):
        """Clean up resources"""
        self.is_recording = False
        
        if self.stream:
            try:
                self.stream.stop_stream()
                self.stream.close()
            except:
                pass
            finally:
                self.stream = None
        
        if self.p:
            try:
                self.p.terminate()
            except:
                pass
            finally:
                self.p = None
    
    def save_recording(self):
        """Save the recorded audio to a WAV file"""
        if not self.frames:
            messagebox.showinfo("Info", "No audio recorded")
            return
        
        try:
            # Ask for save location
            filename = filedialog.asksaveasfilename(
                defaultextension=".wav",
                filetypes=[("WAV files", "*.wav")],
                initialfile=f"recording_{datetime.now().strftime('%Y%m%d_%H%M%S')}.wav"
            )
            
            if not filename:
                return
            
            # Save as WAV file
            p = pyaudio.PyAudio()  # Create a new instance for getting sample size
            with wave.open(filename, 'wb') as wf:
                wf.setnchannels(self.channels)
                wf.setsampwidth(p.get_sample_size(self.format))
                wf.setframerate(self.rate)
                wf.writeframes(b''.join(self.frames))
            p.terminate()
            
            messagebox.showinfo("Success", f"Recording saved to {filename}")
            self.status_label.config(text=f"Saved to {os.path.basename(filename)}")
            
            logger.info(f"Saved recording to {filename}")
            
        except Exception as e:
            logger.error(f"Error saving recording: {e}")
            messagebox.showerror("Error", f"Could not save recording: {str(e)}")

def main():
    try:
        root = tk.Tk()
        app = RobustRecorder(root)
        
        # Handle window close
        def on_closing():
            if app.is_recording:
                if messagebox.askyesno("Confirm", "Recording in progress. Stop and exit?"):
                    app.stop_recording()
                    root.destroy()
            else:
                root.destroy()
        
        root.protocol("WM_DELETE_WINDOW", on_closing)
        root.mainloop()
        
    except Exception as e:
        logger.critical(f"Unhandled exception: {str(e)}")
        messagebox.showerror("Critical Error", f"An unhandled error occurred: {str(e)}")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
