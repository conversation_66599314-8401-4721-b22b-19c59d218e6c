#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
import json
import sys

def check_models(host="http://carlsmacstudio.got.volvo.net:11434"):
    """Check available models on the Ollama server"""
    try:
        # Get models
        print(f"Fetching models from {host}/api/tags...")
        response = requests.get(f"{host}/api/tags")
        
        print(f"Response status: {response.status_code}")
        
        if response.status_code == 200:
            # Pretty print the response
            data = response.json()
            print("\nAvailable models:")
            print(json.dumps(data, indent=2))
            
            # Extract model names
            if 'models' in data:
                models = data['models']
                print("\nModel names:")
                for model in models:
                    print(f"- {model.get('name')}")
            else:
                print("No 'models' key found in response")
                print("Response keys:", list(data.keys()))
        else:
            print(f"Error: {response.status_code}")
            print(response.text)
    except Exception as e:
        print(f"Error: {str(e)}")

def main():
    if len(sys.argv) > 1:
        host = sys.argv[1]
    else:
        host = "http://carlsmacstudio.got.volvo.net:11434"
    
    check_models(host)
    return 0

if __name__ == "__main__":
    sys.exit(main())
