#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import tkinter as tk
from tkinter import ttk
import threading
import time
import numpy as np
import logging
import sys

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# Check for PyAudio
try:
    import pyaudio
    PYAUDIO_AVAILABLE = True
    logger.info("PyAudio is available")
except ImportError:
    PYAUDIO_AVAILABLE = False
    logger.error("PyAudio is not available - this test requires PyAudio")
    sys.exit(1)

class AudioLevelTest:
    def __init__(self, root):
        self.root = root
        self.root.title("Audio Level Test")
        self.root.geometry("600x300")
        
        # Audio parameters
        self.sample_rate = 16000
        self.chunk_size = 1024
        self.channels = 1
        
        # State variables
        self.is_recording = False
        self.audio_level = 0
        self.selected_device_index = None
        self.pyaudio_instance = None
        self.stream = None
        self.recording_thread = None
        
        # Initialize PyAudio
        self.initialize_pyaudio()
        
        # Set up UI
        self.setup_ui()
        
        # Start UI update timer
        self.update_ui()
    
    def initialize_pyaudio(self):
        """Initialize PyAudio and detect devices"""
        try:
            self.pyaudio_instance = pyaudio.PyAudio()
            logger.info(f"PyAudio initialized: {self.pyaudio_instance}")
            
            # Get device count
            device_count = self.pyaudio_instance.get_device_count()
            logger.info(f"Found {device_count} audio devices")
            
            # Log all devices
            for i in range(device_count):
                try:
                    device_info = self.pyaudio_instance.get_device_info_by_index(i)
                    input_channels = device_info.get('maxInputChannels', 0)
                    logger.info(f"Device {i}: {device_info.get('name')} (Input Channels: {input_channels})")
                except Exception as e:
                    logger.error(f"Error getting info for device {i}: {str(e)}")
            
            # Try to get default input device
            try:
                default_device = self.pyaudio_instance.get_default_input_device_info()
                logger.info(f"Default input device: {default_device['name']} (index: {default_device['index']})")
                self.selected_device_index = default_device['index']
            except Exception as e:
                logger.warning(f"Error getting default input device: {str(e)}")
                self.selected_device_index = None
                
                # Try to find any input device
                for i in range(device_count):
                    try:
                        device_info = self.pyaudio_instance.get_device_info_by_index(i)
                        if device_info.get('maxInputChannels', 0) > 0:
                            self.selected_device_index = i
                            logger.info(f"Using first available input device: {device_info['name']} (index: {i})")
                            break
                    except Exception:
                        continue
        except Exception as e:
            logger.error(f"Error initializing PyAudio: {str(e)}")
    
    def setup_ui(self):
        """Set up the user interface"""
        # Main frame
        main_frame = ttk.Frame(self.root, padding=10)
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # Device selection
        device_frame = ttk.LabelFrame(main_frame, text="Audio Device")
        device_frame.pack(fill=tk.X, pady=5)
        
        # Get device list
        devices = []
        if self.pyaudio_instance:
            device_count = self.pyaudio_instance.get_device_count()
            for i in range(device_count):
                try:
                    device_info = self.pyaudio_instance.get_device_info_by_index(i)
                    if device_info.get('maxInputChannels', 0) > 0:
                        is_default = i == self.selected_device_index
                        devices.append(f"{i}: {device_info.get('name')}" + (" (Default)" if is_default else ""))
                except Exception:
                    pass
        
        if not devices:
            devices = ["No input devices found"]
        
        # Device dropdown
        self.device_var = tk.StringVar(value=devices[0] if devices else "")
        self.device_dropdown = ttk.Combobox(device_frame, textvariable=self.device_var, values=devices, state="readonly")
        self.device_dropdown.pack(fill=tk.X, padx=5, pady=5)
        self.device_dropdown.bind("<<ComboboxSelected>>", self.on_device_selected)
        
        # Control panel
        control_frame = ttk.Frame(main_frame)
        control_frame.pack(fill=tk.X, pady=10)
        
        self.record_button = ttk.Button(
            control_frame, 
            text="Start Recording", 
            command=self.toggle_recording
        )
        self.record_button.pack(side=tk.LEFT, padx=5)
        
        # Audio level display
        level_frame = ttk.LabelFrame(main_frame, text="Audio Level")
        level_frame.pack(fill=tk.BOTH, expand=True, pady=10)
        
        # Level meter
        self.level_canvas = tk.Canvas(level_frame, height=50, bg="white")
        self.level_canvas.pack(fill=tk.X, padx=5, pady=5)
        
        # Numeric level display
        self.level_label = ttk.Label(level_frame, text="Level: 0.0")
        self.level_label.pack(pady=5)
        
        # Status bar
        self.status_bar = ttk.Label(
            self.root, 
            text="Ready", 
            relief=tk.SUNKEN, 
            anchor=tk.W
        )
        self.status_bar.pack(side=tk.BOTTOM, fill=tk.X)
    
    def on_device_selected(self, event):
        """Handle device selection"""
        if self.is_recording:
            logger.warning("Cannot change device while recording")
            return
        
        selected = self.device_dropdown.get()
        if selected and ":" in selected:
            device_index = int(selected.split(":")[0])
            self.selected_device_index = device_index
            logger.info(f"Selected device index: {device_index}")
            self.status_bar.config(text=f"Selected device: {selected}")
    
    def toggle_recording(self):
        """Start or stop recording"""
        if not self.is_recording:
            self.start_recording()
        else:
            self.stop_recording()
    
    def start_recording(self):
        """Start recording and measuring audio levels"""
        if self.is_recording:
            return
        
        if self.selected_device_index is None:
            logger.error("No input device selected")
            self.status_bar.config(text="Error: No input device selected")
            return
        
        try:
            # Open audio stream
            self.stream = self.pyaudio_instance.open(
                format=pyaudio.paInt16,
                channels=self.channels,
                rate=self.sample_rate,
                input=True,
                input_device_index=self.selected_device_index,
                frames_per_buffer=self.chunk_size
            )
            
            logger.info(f"Audio stream opened with device index {self.selected_device_index}")
            
            # Start recording thread
            self.is_recording = True
            self.recording_thread = threading.Thread(
                target=self._record_audio,
                daemon=True
            )
            self.recording_thread.start()
            
            # Update UI
            self.record_button.config(text="Stop Recording")
            self.status_bar.config(text="Recording...")
            
        except Exception as e:
            logger.error(f"Error starting recording: {str(e)}")
            self.status_bar.config(text=f"Error: {str(e)}")
    
    def stop_recording(self):
        """Stop recording"""
        if not self.is_recording:
            return
        
        self.is_recording = False
        
        # Close stream
        if self.stream:
            try:
                self.stream.stop_stream()
                self.stream.close()
                self.stream = None
            except Exception as e:
                logger.error(f"Error closing stream: {str(e)}")
        
        # Update UI
        self.record_button.config(text="Start Recording")
        self.status_bar.config(text="Ready")
        self.audio_level = 0
        self.update_level_display(0)
    
    def _record_audio(self):
        """Record audio and measure levels"""
        while self.is_recording and self.stream:
            try:
                # Read audio data
                audio_data = self.stream.read(self.chunk_size, exception_on_overflow=False)
                audio_array = np.frombuffer(audio_data, dtype=np.int16)
                
                # Calculate level
                level = np.abs(audio_array).mean()
                normalized_level = min(100, level / 50)  # Normalize to 0-100
                
                # Update level
                self.audio_level = normalized_level
                logger.debug(f"Audio level: {level:.2f} (normalized: {normalized_level:.2f})")
                
            except Exception as e:
                logger.error(f"Error reading audio: {str(e)}")
                time.sleep(0.1)  # Avoid tight loop on errors
    
    def update_level_display(self, level):
        """Update the audio level visualization"""
        # Update canvas
        self.level_canvas.delete("all")
        width = self.level_canvas.winfo_width()
        height = self.level_canvas.winfo_height()
        
        # Draw background
        self.level_canvas.create_rectangle(0, 0, width, height, fill="white")
        
        # Draw level bar
        bar_width = int(width * (level / 100))
        
        # Color based on level
        if level < 30:
            color = "green"
        elif level < 70:
            color = "yellow"
        else:
            color = "red"
        
        self.level_canvas.create_rectangle(0, 0, bar_width, height, fill=color)
        
        # Update label
        self.level_label.config(text=f"Level: {level:.1f}")
    
    def update_ui(self):
        """Periodically update the UI"""
        if self.is_recording:
            self.update_level_display(self.audio_level)
        
        # Schedule next update
        self.root.after(50, self.update_ui)
    
    def cleanup(self):
        """Clean up resources"""
        self.stop_recording()
        if self.pyaudio_instance:
            self.pyaudio_instance.terminate()
            self.pyaudio_instance = None

def main():
    root = tk.Tk()
    app = AudioLevelTest(root)
    
    # Handle window close
    def on_closing():
        app.cleanup()
        root.destroy()
    
    root.protocol("WM_DELETE_WINDOW", on_closing)
    root.mainloop()

if __name__ == "__main__":
    main()
